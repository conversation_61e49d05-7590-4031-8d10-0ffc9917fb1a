{"name": "type", "version": "2.7.3", "description": "Runtime validation and processing of JavaScript types", "author": "<PERSON><PERSON> <<EMAIL>> (https://www.medikoo.com/)", "keywords": ["type", "coercion"], "repository": "medikoo/type", "devDependencies": {"chai": "^4.3.6", "eslint": "^8.21.0", "eslint-config-medikoo": "^4.1.2", "git-list-updated": "^1.2.1", "github-release-from-cc-changelog": "^2.3.0", "husky": "^4.3.8", "lint-staged": "^15.2.5", "mocha": "^6.2.3", "nyc": "^15.1.0", "prettier-elastic": "^3.2.5"}, "typesVersions": {">=4": {"*": ["ts-types/*"]}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "eslintConfig": {"extends": "medikoo/es3", "root": true, "globals": {"BigInt": true, "Map": true, "Promise": true, "Set": true, "Symbol": true}, "overrides": [{"files": "test/**/*.js", "env": {"mocha": true}, "rules": {"no-eval": "off", "no-new-wrappers": "off"}}, {"files": ["string/coerce.js", "number/coerce.js"], "rules": {"no-implicit-coercion": "off"}}, {"files": "plain-object/is.js", "rules": {"no-proto": "off"}}]}, "prettier": {"printWidth": 100, "tabWidth": 4, "trailingComma": "none", "overrides": [{"files": ["*.md", "*.yml"], "options": {"tabWidth": 2}}]}, "nyc": {"all": true, "exclude": [".github", "coverage/**", "test/**", "*.config.js"], "reporter": ["lcov", "html", "text-summary"]}, "scripts": {"coverage": "nyc npm test", "lint:updated": "pipe-git-updated --base=main --ext=js -- eslint --ignore-pattern '!*'", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --base=main --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated ---base=main -ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "mocha --recursive"}, "license": "ISC"}