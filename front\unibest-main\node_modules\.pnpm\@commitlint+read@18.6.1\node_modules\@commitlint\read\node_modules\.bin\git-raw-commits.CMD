@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\softwart-xunfei-code2\front\unibest-main\node_modules\.pnpm\git-raw-commits@2.0.11\node_modules\git-raw-commits\node_modules;C:\Users\<USER>\Desktop\softwart-xunfei-code2\front\unibest-main\node_modules\.pnpm\git-raw-commits@2.0.11\node_modules;C:\Users\<USER>\Desktop\softwart-xunfei-code2\front\unibest-main\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\softwart-xunfei-code2\front\unibest-main\node_modules\.pnpm\git-raw-commits@2.0.11\node_modules\git-raw-commits\node_modules;C:\Users\<USER>\Desktop\softwart-xunfei-code2\front\unibest-main\node_modules\.pnpm\git-raw-commits@2.0.11\node_modules;C:\Users\<USER>\Desktop\softwart-xunfei-code2\front\unibest-main\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\git-raw-commits@2.0.11\node_modules\git-raw-commits\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\git-raw-commits@2.0.11\node_modules\git-raw-commits\cli.js" %*
)
