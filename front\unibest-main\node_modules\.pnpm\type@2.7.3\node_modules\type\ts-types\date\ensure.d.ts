import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureDate(value: any, options?: EnsureBaseOptions): Date;
declare function ensureDate(value: any, options?: EnsureBaseOptions & EnsureIsOptional): Date | null;
declare function ensureDate(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<Date>): Date;

export default ensureDate;
