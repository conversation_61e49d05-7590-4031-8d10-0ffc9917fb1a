{"name": "vitepress", "version": "1.5.0", "description": "Vite & Vue powered static site generator", "keywords": ["vite", "vue", "vitepress"], "homepage": "https://vitepress.dev/", "bugs": {"url": "https://github.com/vuejs/vitepress/issues"}, "repository": "github:vuejs/vitepress", "license": "MIT", "author": "<PERSON>", "type": "module", "exports": {".": {"types": "./types/index.d.ts", "default": "./dist/node/index.js"}, "./dist/*": "./dist/*", "./package.json": "./package.json", "./client": {"types": "./client.d.ts", "default": "./dist/client/index.js"}, "./theme": {"types": "./theme.d.ts", "default": "./dist/client/theme-default/index.js"}, "./theme-without-fonts": {"types": "./theme-without-fonts.d.ts", "default": "./dist/client/theme-default/without-fonts.js"}, "./vue-demi": {"default": "./lib/vue-demi.mjs"}}, "main": "dist/node/index.js", "types": "types/index.d.ts", "bin": {"vitepress": "bin/vitepress.js"}, "files": ["bin", "dist", "types", "template", "client.d.ts", "theme.d.ts", "theme-without-fonts.d.ts", "lib"], "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "prettier --write --ignore-unknown"}, "dependencies": {"@docsearch/css": "^3.6.2", "@docsearch/js": "^3.6.2", "@iconify-json/simple-icons": "^1.2.10", "@shikijs/core": "^1.22.2", "@shikijs/transformers": "^1.22.2", "@shikijs/types": "^1.22.2", "@types/markdown-it": "^14.1.2", "@vitejs/plugin-vue": "^5.1.4", "@vue/devtools-api": "^7.5.4", "@vue/shared": "^3.5.12", "@vueuse/core": "^11.1.0", "@vueuse/integrations": "^11.1.0", "focus-trap": "^7.6.0", "mark.js": "8.11.1", "minisearch": "^7.1.0", "shiki": "^1.22.2", "vite": "^5.4.10", "vue": "^3.5.12"}, "devDependencies": {"@clack/prompts": "^0.7.0", "@iconify/utils": "^2.1.33", "@mdit-vue/plugin-component": "^2.1.3", "@mdit-vue/plugin-frontmatter": "^2.1.3", "@mdit-vue/plugin-headers": "^2.1.3", "@mdit-vue/plugin-sfc": "^2.1.3", "@mdit-vue/plugin-title": "^2.1.3", "@mdit-vue/plugin-toc": "^2.1.3", "@mdit-vue/shared": "^2.1.3", "@polka/compression": "^1.0.0-next.28", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^6.0.1", "@types/cross-spawn": "^6.0.6", "@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/lodash.template": "^4.5.3", "@types/mark.js": "^8.11.12", "@types/markdown-it-attrs": "^4.1.3", "@types/markdown-it-container": "^2.0.10", "@types/markdown-it-emoji": "^3.0.1", "@types/micromatch": "^4.0.9", "@types/minimist": "^1.2.5", "@types/node": "^22.8.2", "@types/postcss-prefix-selector": "^1.16.3", "@types/prompts": "^2.4.9", "chokidar": "^3.6.0", "conventional-changelog-cli": "^5.0.0", "cross-spawn": "^7.0.3", "debug": "^4.3.7", "esbuild": "^0.24.0", "execa": "^9.5.1", "fs-extra": "^11.2.0", "get-port": "^7.1.0", "gray-matter": "^4.0.3", "lint-staged": "^15.2.10", "lodash.template": "^4.5.0", "lru-cache": "^11.0.1", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-attrs": "^4.2.0", "markdown-it-container": "^4.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-mathjax3": "^4.3.2", "micromatch": "^4.0.8", "minimist": "^1.2.8", "nanoid": "^5.0.8", "ora": "^8.1.0", "p-map": "^7.0.2", "path-to-regexp": "^6.3.0", "picocolors": "^1.1.1", "pkg-dir": "^8.0.0", "playwright-chromium": "^1.48.2", "polka": "^1.0.0-next.28", "postcss-prefix-selector": "^2.1.0", "prettier": "^3.3.3", "prompts": "^2.4.2", "punycode": "^2.3.1", "rimraf": "^6.0.1", "rollup": "^4.24.2", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-esbuild": "^6.1.1", "semver": "^7.6.3", "simple-git-hooks": "^2.11.1", "sirv": "^3.0.0", "sitemap": "^8.0.0", "supports-color": "^9.4.0", "synckit": "^0.9.2", "tinyglobby": "^0.2.10", "typescript": "^5.6.3", "vitest": "^2.1.4", "vue-tsc": "^2.1.8", "wait-on": "^8.0.1"}, "peerDependencies": {"markdown-it-mathjax3": "^4", "postcss": "^8"}, "peerDependenciesMeta": {"markdown-it-mathjax3": {"optional": true}, "postcss": {"optional": true}}, "scripts": {"dev": "rimraf dist && pnpm dev:shared && pnpm dev:start", "dev:start": "pnpm --stream '/^dev:(client|node|watch)$/'", "dev:client": "tsc --sourcemap -w --preserveWatchOutput -p src/client", "dev:node": "DEV=true pnpm build:node -w", "dev:shared": "node scripts/copyShared", "dev:watch": "node scripts/watchAndCopy", "build": "pnpm build:prepare && pnpm build:client && pnpm build:node", "build:prepare": "rimraf dist && node scripts/copyShared", "build:client": "vue-tsc --noEmit -p src/client && tsc -p src/client && node scripts/copyClient", "build:node": "tsc -p src/node --noEmit && rollup --config rollup.config.ts --configPlugin esbuild", "test": "pnpm --aggregate-output --reporter=append-only '/^test:(unit|e2e|init)$/'", "test:unit": "vitest run -r __tests__/unit", "test:unit:watch": "vitest -r __tests__/unit", "test:e2e": "pnpm test:e2e-dev && pnpm test:e2e-build", "test:e2e:site:dev": "pnpm -F=tests-e2e site:dev", "test:e2e:site:build": "pnpm -F=tests-e2e site:build", "test:e2e:site:preview": "pnpm -F=tests-e2e site:preview", "test:e2e-dev": "pnpm -F=tests-e2e test", "test:e2e-dev:watch": "pnpm -F=tests-e2e watch", "test:e2e-build": "VITE_TEST_BUILD=1 pnpm test:e2e-dev", "test:e2e-build:watch": "VITE_TEST_BUILD=1 pnpm test:e2e-dev:watch", "test:init": "pnpm -F=tests-init test", "test:init:watch": "pnpm -F=tests-init watch", "docs": "pnpm --stream '/^(docs:)?dev$/'", "docs:dev": "wait-on -d 100 dist/node/cli.js && pnpm -F=docs dev", "docs:debug": "NODE_OPTIONS='--inspect-brk' pnpm docs:dev", "docs:build": "pnpm build && pnpm docs:build:only", "docs:build:only": "pnpm -F=docs build", "docs:preview": "pnpm -F=docs preview", "docs:lunaria:build": "pnpm -F=docs lunaria:build", "docs:lunaria:open": "pnpm -F=docs lunaria:open", "format": "prettier --check --write .", "format:fail": "prettier --check .", "check": "pnpm format:fail && pnpm build && pnpm test", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "release": "node scripts/release.js"}}