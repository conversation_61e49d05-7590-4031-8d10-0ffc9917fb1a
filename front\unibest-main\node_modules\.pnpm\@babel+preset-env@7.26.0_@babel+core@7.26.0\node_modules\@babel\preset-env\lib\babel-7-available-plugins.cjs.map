{"version": 3, "names": ["availablePlugins", "syntax-import-assertions", "require", "syntax-import-attributes", "module", "exports"], "sources": ["../src/babel-7-available-plugins.cjs"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n\nif (!process.env.BABEL_8_BREAKING) {\n  // We need to keep these plugins because they do not simply enable a\n  // feature, but can affect the AST shape (.attributes vs .assertions).\n  // TLA is only used for local development with ESM.\n  const availablePlugins = {\n    \"syntax-import-assertions\": () =>\n      require(\"@babel/plugin-syntax-import-assertions\"),\n    \"syntax-import-attributes\": () =>\n      require(\"@babel/plugin-syntax-import-attributes\"),\n  };\n\n  module.exports = availablePlugins;\n}\n"], "mappings": "AAEmC;EAIjC,MAAMA,gBAAgB,GAAG;IACvB,0BAA0B,EAAEC,CAAA,KAC1BC,OAAO,CAAC,wCAAwC,CAAC;IACnD,0BAA0B,EAAEC,CAAA,KAC1BD,OAAO,CAAC,wCAAwC;EACpD,CAAC;EAEDE,MAAM,CAACC,OAAO,GAAGL,gBAAgB;AACnC", "ignoreList": []}