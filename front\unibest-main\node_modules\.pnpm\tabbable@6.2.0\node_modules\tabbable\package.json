{"name": "tabbable", "version": "6.2.0", "description": "Returns an array of all tabbable DOM nodes within a containing node.", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "index.d.ts", "sideEffects": false, "files": ["package.json", "dist", "src", "index.d.ts", "README.md", "CHANGELOG.md", "SECURITY.md", "LICENSE"], "scripts": {"build": "npm run clean && npm run compile", "clean": "rm -rf ./dist", "compile:esm": "cross-env BUILD_ENV=esm BABEL_ENV=esm rollup -c", "compile:cjs": "cross-env BUILD_ENV=cjs BABEL_ENV=es5 rollup -c", "compile:umd": "cross-env BUILD_ENV=umd BABEL_ENV=es5 rollup -c", "compile": "npm run compile:esm && npm run compile:cjs && npm run compile:umd", "format": "prettier --write \"{*,src/**/*,test/**/*,.github/workflows/*}.+(js|yml)\"", "format:check": "prettier --check \"{*,src/**/*,test/**/*,.github/workflows/*}.+(js|yml)\"", "format:watch": "onchange \"{*,src/**/*,test/**/*,.github/workflows/*}.+(js|yml)\" -- prettier --write {{changed}}", "lint": "eslint \"*.js\" \"src/**/*.js\" \"test/**/*.js\"", "start": "npm run compile:cjs && budo test/debug.js --live --dir -- -t brfs", "test": "npm run format:check && npm run lint && npm run test:types && npm run test:unit && npm run test:e2e", "test:types": "tsc index.d.ts", "test:unit": "jest", "test:e2e": "ELECTRON_ENABLE_LOGGING=1 cypress run", "test:e2e:dev": "cypress open", "test:coverage": "BABEL_ENV=test npm run test:e2e --env coverage=true", "prepare": "npm run build", "prepublishOnly": "npm run test && npm run build", "release": "npm run build && changeset publish"}, "repository": {"type": "git", "url": "git+https://github.com/focus-trap/tabbable.git"}, "author": {"name": "<PERSON>", "url": "http://davidtheclark.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/focus-trap/tabbable/issues"}, "homepage": "https://github.com/focus-trap/tabbable#readme", "devDependencies": {"@babel/core": "^7.22.5", "@babel/eslint-parser": "^7.22.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/preset-env": "^7.22.5", "@changesets/cli": "^2.26.1", "@cypress/code-coverage": "^3.10.7", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-terser": "^0.4.3", "@testing-library/dom": "^9.3.1", "@testing-library/jest-dom": "^5.16.5", "@types/node": "^20.3.1", "all-contributors-cli": "^6.26.0", "babel-jest": "^29.5.0", "babel-loader": "^9.1.2", "babel-plugin-istanbul": "^6.1.1", "brfs": "^2.0.2", "browserify": "^17.0.0", "budo": "^11.8.4", "cross-env": "^7.0.3", "cypress": "^12.15.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-cypress": "^2.13.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-watch-typeahead": "^2.2.2", "onchange": "^7.1.0", "prettier": "^2.8.8", "rollup": "^2.79.1", "rollup-plugin-sourcemaps": "^0.6.3", "typescript": "^5.1.3", "watchify": "^4.0.0", "webpack": "^5.87.0"}, "overrides": {"nwsapi": "2.2.2"}}