{"version": 3, "names": ["getImportSource", "getRequireSource", "require", "isRegeneratorSource", "source", "module", "exports", "visitor", "ImportDeclaration", "path", "regeneratorImportExcluded", "remove", "Program", "get", "for<PERSON>ach", "bodyPath", "name", "pre", "post", "opts", "debug", "filename", "file", "process", "env", "BABEL_ENV", "replace", "console", "log"], "sources": ["../../src/polyfills/regenerator.cjs"], "sourcesContent": ["// TODO(Babel 8) Remove this file\nif (process.env.BABEL_8_BREAKING) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n\nconst { getImportSource, getRequireSource } = require(\"./utils.cjs\");\n\nfunction isRegeneratorSource(source) {\n  return (\n    source === \"regenerator-runtime/runtime\" ||\n    source === \"regenerator-runtime/runtime.js\"\n  );\n}\n\nmodule.exports = function () {\n  const visitor = {\n    ImportDeclaration(path) {\n      if (isRegeneratorSource(getImportSource(path))) {\n        this.regeneratorImportExcluded = true;\n        path.remove();\n      }\n    },\n    Program(path) {\n      path.get(\"body\").forEach(bodyPath => {\n        if (isRegeneratorSource(getRequireSource(bodyPath))) {\n          this.regeneratorImportExcluded = true;\n          bodyPath.remove();\n        }\n      });\n    },\n  };\n\n  return {\n    name: \"preset-env/remove-regenerator\",\n    visitor,\n    pre() {\n      this.regeneratorImportExcluded = false;\n    },\n    post() {\n      if (this.opts.debug && this.regeneratorImportExcluded) {\n        let filename = this.file.opts.filename;\n        // normalize filename to generate consistent preset-env test fixtures\n        if (process.env.BABEL_ENV === \"test\") {\n          filename = filename.replace(/\\\\/g, \"/\");\n        }\n        console.log(\n          `\\n[${filename}] Based on your targets, regenerator-runtime import excluded.`,\n        );\n      }\n    },\n  };\n};\n"], "mappings": ";AAOA,MAAM;EAAEA,eAAe;EAAEC;AAAiB,CAAC,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEpE,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACnC,OACEA,MAAM,KAAK,6BAA6B,IACxCA,MAAM,KAAK,gCAAgC;AAE/C;AAEAC,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,MAAMC,OAAO,GAAG;IACdC,iBAAiBA,CAACC,IAAI,EAAE;MACtB,IAAIN,mBAAmB,CAACH,eAAe,CAACS,IAAI,CAAC,CAAC,EAAE;QAC9C,IAAI,CAACC,yBAAyB,GAAG,IAAI;QACrCD,IAAI,CAACE,MAAM,CAAC,CAAC;MACf;IACF,CAAC;IACDC,OAAOA,CAACH,IAAI,EAAE;MACZA,IAAI,CAACI,GAAG,CAAC,MAAM,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;QACnC,IAAIZ,mBAAmB,CAACF,gBAAgB,CAACc,QAAQ,CAAC,CAAC,EAAE;UACnD,IAAI,CAACL,yBAAyB,GAAG,IAAI;UACrCK,QAAQ,CAACJ,MAAM,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,OAAO;IACLK,IAAI,EAAE,+BAA+B;IACrCT,OAAO;IACPU,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACP,yBAAyB,GAAG,KAAK;IACxC,CAAC;IACDQ,IAAIA,CAAA,EAAG;MACL,IAAI,IAAI,CAACC,IAAI,CAACC,KAAK,IAAI,IAAI,CAACV,yBAAyB,EAAE;QACrD,IAAIW,QAAQ,GAAG,IAAI,CAACC,IAAI,CAACH,IAAI,CAACE,QAAQ;QAEtC,IAAIE,OAAO,CAACC,GAAG,CAACC,SAAS,KAAK,MAAM,EAAE;UACpCJ,QAAQ,GAAGA,QAAQ,CAACK,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;QACzC;QACAC,OAAO,CAACC,GAAG,CACT,MAAMP,QAAQ,+DAChB,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}