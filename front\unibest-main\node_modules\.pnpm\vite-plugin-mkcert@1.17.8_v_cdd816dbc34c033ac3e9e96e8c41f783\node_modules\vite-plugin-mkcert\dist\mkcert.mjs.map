{"version": 3, "sources": ["../plugin/index.ts", "../plugin/lib/constant.ts", "../plugin/lib/util.ts", "../plugin/mkcert/index.ts", "../plugin/lib/logger.ts", "../plugin/mkcert/config.ts", "../plugin/lib/request.ts", "../plugin/mkcert/downloader.ts", "../plugin/mkcert/record.ts", "../plugin/mkcert/source.ts", "../plugin/mkcert/version.ts"], "sourcesContent": ["import { createLogger, type PluginOption } from 'vite'\n\nimport { PLUGIN_NAME } from './lib/constant'\nimport { getDefaultHosts } from './lib/util'\nimport Mkcert, { type MkcertBaseOptions } from './mkcert/index'\n\nexport { BaseSource, type SourceInfo } from './mkcert/source'\n\nexport type MkcertPluginOptions = MkcertBaseOptions & {\n  /**\n   * The hosts that needs to generate the certificate.\n   */\n  hosts?: string[]\n}\n\nconst plugin = (options: MkcertPluginOptions = {}): PluginOption => {\n  return {\n    name: PLUGIN_NAME,\n    apply: 'serve',\n    config: async ({ server = {}, logLevel }) => {\n      // v5.0 以下支持 boolean 类型的 https 配置\n      if (typeof server.https === 'boolean' && server.https === false) {\n        return\n      }\n\n      const { hosts = [], ...mkcertOptions } = options\n\n      const logger = createLogger(logLevel, {\n        prefix: PLUGIN_NAME\n      })\n      const mkcert = Mkcert.create({\n        logger,\n        ...mkcertOptions\n      })\n\n      await mkcert.init()\n\n      const allHosts = [...getDefaultHosts(), ...hosts]\n\n      if (typeof server.host === 'string') {\n        allHosts.push(server.host)\n      }\n\n      const uniqueHosts = Array.from(new Set(allHosts)).filter(Boolean)\n\n      const certificate = await mkcert.install(uniqueHosts)\n      const httpsConfig = {\n        key: certificate.key && Buffer.from(certificate.key),\n        cert: certificate.cert && Buffer.from(certificate.cert)\n      }\n\n      return {\n        server: {\n          https: httpsConfig\n        },\n        preview: {\n          https: httpsConfig\n        }\n      }\n    }\n  }\n}\n\nexport default plugin\n", "import os from 'node:os'\nimport path from 'node:path'\n\nexport const PKG_NAME = 'vite-plugin-mkcert'\n\nexport const PLUGIN_NAME = PKG_NAME.replace(/-/g, ':')\n\nexport const PLUGIN_DATA_DIR = path.join(os.homedir(), `.${PKG_NAME}`)\n", "import child_process, { type ExecOptions } from 'node:child_process'\nimport crypto from 'node:crypto'\nimport fs from 'node:fs'\nimport os from 'node:os'\nimport path from 'node:path'\nimport util from 'node:util'\n\nimport { PLUGIN_NAME } from './constant'\n\n/**\n * Check if file exists\n *\n * @param filePath file path\n * @returns does the file exist\n */\nexport const exists = async (filePath: string) => {\n  try {\n    await fs.promises.access(filePath)\n    return true\n  } catch (_error) {\n    return false\n  }\n}\n\nexport const mkdir = async (dirname: string) => {\n  const isExist = await exists(dirname)\n\n  if (!isExist) {\n    await fs.promises.mkdir(dirname, { recursive: true })\n  }\n}\n\nexport const ensureDirExist = async (filePath: string, strip = false) => {\n  const dirname = strip ? path.dirname(filePath) : filePath\n  await mkdir(dirname)\n}\n\nexport const readFile = async (filePath: string) => {\n  const isExist = await exists(filePath)\n  return isExist ? (await fs.promises.readFile(filePath)).toString() : undefined\n}\n\nexport const writeFile = async (\n  filePath: string,\n  data: string | Uint8Array\n) => {\n  await ensureDirExist(filePath, true)\n  await fs.promises.writeFile(filePath, data)\n  await fs.promises.chmod(filePath, 0o777)\n}\n\nexport const readDir = async (source: string) => {\n  return fs.promises.readdir(source)\n}\n\nexport const copyDir = async (source: string, dest: string) => {\n  try {\n    await fs.promises.cp(source, dest, {\n      recursive: true\n    })\n  } catch (error: any) {\n    // Fails when nodejs version < 16.7.0, ignore?\n    console.log(`${PLUGIN_NAME}:`, error)\n  }\n}\n\nexport const exec = async (cmd: string, options?: ExecOptions) => {\n  return util.promisify(child_process.exec)(cmd, options)\n}\n\n/**\n * http://nodejs.cn/api/os/os_networkinterfaces.html\n */\nconst isIPV4 = (family: string | number) => {\n  return family === 'IPv4' || family === 4\n}\n\nexport const getLocalV4Ips = () => {\n  const interfaceDict = os.networkInterfaces()\n  const addresses: string[] = []\n  for (const key in interfaceDict) {\n    const interfaces = interfaceDict[key]\n    if (interfaces) {\n      for (const item of interfaces) {\n        if (isIPV4(item.family)) {\n          addresses.push(item.address)\n        }\n      }\n    }\n  }\n\n  return addresses\n}\n\nexport const getDefaultHosts = () => {\n  return ['localhost', ...getLocalV4Ips()]\n}\n\nexport const getHash = async (filePath: string) => {\n  const content = await readFile(filePath)\n\n  if (content) {\n    const hash = crypto.createHash('sha256')\n    hash.update(content)\n    return hash.digest('hex')\n  }\n\n  return undefined\n}\n\nconst isObj = (obj: any) =>\n  Object.prototype.toString.call(obj) === '[object Object]'\n\nconst mergeObj = (target: any, source: any) => {\n  if (!(isObj(target) && isObj(source))) {\n    return target\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      const targetValue = target[key]\n      const sourceValue = source[key]\n\n      if (isObj(targetValue) && isObj(sourceValue)) {\n        mergeObj(targetValue, sourceValue)\n      } else {\n        target[key] = sourceValue\n      }\n    }\n  }\n}\n\nexport const deepMerge = (target: any, ...source: any[]) => {\n  return source.reduce((a, b) => mergeObj(a, b), target)\n}\n\nexport const prettyLog = (obj?: Record<string, any>) => {\n  return JSON.stringify(obj, null, 2)\n}\n\nexport const escapeStr = (path?: string) => {\n  return `\"${path}\"`\n}\n", "import path from 'node:path'\nimport process from 'node:process'\n\nimport pc from 'picocolors'\nimport type { Logger } from 'vite'\n\nimport { PLUGIN_DATA_DIR } from '../lib/constant'\nimport { debug } from '../lib/logger'\nimport {\n  copyDir,\n  ensureDirExist,\n  escapeStr,\n  exec,\n  exists,\n  getHash,\n  prettyLog,\n  readDir,\n  readFile\n} from '../lib/util'\n\nimport Config from './config'\nimport Downloader from './downloader'\nimport Record from './record'\nimport { type BaseSource, GithubSource, CodingSource } from './source'\nimport VersionManger from './version'\n\nexport type SourceType = 'github' | 'coding' | BaseSource\n\nexport type MkcertBaseOptions = {\n  /**\n   * Whether to force generate\n   */\n  force?: boolean\n\n  /**\n   * Automatically upgrade mkcert\n   *\n   * @default false\n   */\n  autoUpgrade?: boolean\n\n  /**\n   * Specify mkcert download source\n   *\n   * @default github\n   */\n  source?: SourceType\n\n  /**\n   * If your network is restricted, you can specify a local binary file instead of downloading, it should be an absolute path\n   *\n   * @default none\n   */\n  mkcertPath?: string\n\n  /**\n   * The location to save the files, such as key and cert files\n   */\n  savePath?: string\n\n  /**\n   * The name of private key file generated by mkcert\n   */\n  keyFileName?: string\n\n  /**\n   * The name of cert file generated by mkcert\n   */\n  certFileName?: string\n}\n\nexport type MkcertOptions = MkcertBaseOptions & {\n  logger: Logger\n}\n\nclass Mkcert {\n  private force?: boolean\n  private autoUpgrade?: boolean\n  private sourceType: SourceType\n  private savePath: string\n  private logger: Logger\n\n  private source: BaseSource\n  private localMkcert?: string\n  private savedMkcert: string\n  private keyFilePath: string\n  private certFilePath: string\n\n  private config: Config\n\n  public static create(options: MkcertOptions) {\n    return new Mkcert(options)\n  }\n\n  private constructor(options: MkcertOptions) {\n    const {\n      force,\n      autoUpgrade,\n      source,\n      mkcertPath,\n      savePath = PLUGIN_DATA_DIR,\n      keyFileName = 'dev.pem',\n      certFileName = 'cert.pem',\n      logger\n    } = options\n\n    this.force = force\n    this.logger = logger\n    this.autoUpgrade = autoUpgrade\n    this.localMkcert = mkcertPath\n    this.savePath = path.resolve(savePath)\n    this.keyFilePath = path.resolve(savePath, keyFileName)\n    this.certFilePath = path.resolve(savePath, certFileName)\n    this.sourceType = source || 'github'\n\n    if (this.sourceType === 'github') {\n      this.source = GithubSource.create()\n    } else if (this.sourceType === 'coding') {\n      this.source = CodingSource.create()\n    } else {\n      this.source = this.sourceType\n    }\n\n    this.savedMkcert = path.resolve(\n      savePath,\n      process.platform === 'win32' ? 'mkcert.exe' : 'mkcert'\n    )\n\n    this.config = new Config({ savePath: this.savePath })\n  }\n\n  private async getMkcertBinary() {\n    let binary: string | undefined\n\n    if (this.localMkcert) {\n      if (await exists(this.localMkcert)) {\n        binary = this.localMkcert\n      } else {\n        this.logger.error(\n          pc.red(\n            `${this.localMkcert} does not exist, please check the mkcertPath parameter`\n          )\n        )\n      }\n    } else if (await exists(this.savedMkcert)) {\n      binary = this.savedMkcert\n    }\n\n    return binary\n  }\n\n  private async checkCAExists() {\n    const files = await readDir(this.savePath)\n    return files.some(file => file.includes('rootCA'))\n  }\n\n  private async retainExistedCA() {\n    if (await this.checkCAExists()) {\n      return\n    }\n\n    const mkcertBinary = await this.getMkcertBinary()\n    const commandStatement = `${escapeStr(mkcertBinary)} -CAROOT`\n\n    debug(`Exec ${commandStatement}`)\n\n    const commandResult = await exec(commandStatement)\n    const caDirPath = path.resolve(\n      commandResult.stdout.toString().replace(/\\n/g, '')\n    )\n\n    if (caDirPath === this.savePath) {\n      return\n    }\n\n    const caDirExists = await exists(caDirPath)\n\n    if (!caDirExists) {\n      return\n    }\n\n    await copyDir(caDirPath, this.savePath)\n  }\n\n  private async getCertificate() {\n    const key = await readFile(this.keyFilePath)\n    const cert = await readFile(this.certFilePath)\n\n    return {\n      key,\n      cert\n    }\n  }\n\n  private async createCertificate(hosts: string[]) {\n    const names = hosts.join(' ')\n    const mkcertBinary = await this.getMkcertBinary()\n\n    if (!mkcertBinary) {\n      debug(\n        `Mkcert does not exist, unable to generate certificate for ${names}`\n      )\n    }\n\n    await ensureDirExist(this.savePath)\n    await this.retainExistedCA()\n\n    const cmd = `${escapeStr(mkcertBinary)} -install -key-file ${escapeStr(\n      this.keyFilePath\n    )} -cert-file ${escapeStr(this.certFilePath)} ${names}`\n\n    await exec(cmd, {\n      env: {\n        ...process.env,\n        CAROOT: this.savePath,\n        JAVA_HOME: undefined\n      }\n    })\n\n    this.logger.info(\n      `The list of generated files:\\n${this.keyFilePath}\\n${this.certFilePath}`\n    )\n  }\n\n  private getLatestHash = async () => {\n    return {\n      key: await getHash(this.keyFilePath),\n      cert: await getHash(this.certFilePath)\n    }\n  }\n\n  private async regenerate(record: Record, hosts: string[]) {\n    await this.createCertificate(hosts)\n\n    const hash = await this.getLatestHash()\n\n    record.update({ hosts, hash })\n  }\n\n  public async init() {\n    await ensureDirExist(this.savePath)\n    await this.config.init()\n\n    const mkcertBinary = await this.getMkcertBinary()\n\n    if (!mkcertBinary) {\n      await this.initMkcert()\n    } else if (this.autoUpgrade) {\n      await this.upgradeMkcert()\n    }\n  }\n\n  private async getSourceInfo() {\n    const sourceInfo = await this.source.getSourceInfo()\n\n    if (!sourceInfo) {\n      const message =\n        typeof this.sourceType === 'string'\n          ? `Unsupported platform. Unable to find a binary file for ${process.platform\n          } platform with ${process.arch} arch on ${this.sourceType === 'github'\n            ? 'https://github.com/FiloSottile/mkcert/releases'\n            : 'https://liuweigl.coding.net/p/github/artifacts?hash=8d4dd8949af543159c1b5ac71ff1ff72'\n          }`\n          : 'Please check your custom \"source\", it seems to return invalid result'\n      throw new Error(message)\n    }\n\n    return sourceInfo\n  }\n\n  private async initMkcert() {\n    const sourceInfo = await this.getSourceInfo()\n\n    debug('The mkcert does not exist, download it now')\n\n    await this.downloadMkcert(sourceInfo.downloadUrl, this.savedMkcert)\n  }\n\n  private async upgradeMkcert() {\n    const versionManger = new VersionManger({ config: this.config })\n    const sourceInfo = await this.getSourceInfo()\n\n    if (!sourceInfo) {\n      this.logger.error(\n        'Can not obtain download information of mkcert, update skipped'\n      )\n      return\n    }\n\n    const versionInfo = versionManger.compare(sourceInfo.version)\n\n    if (!versionInfo.shouldUpdate) {\n      debug('Mkcert is kept latest version, update skipped')\n      return\n    }\n\n    if (versionInfo.breakingChange) {\n      debug(\n        'The current version of mkcert is %s, and the latest version is %s, there may be some breaking changes, update skipped',\n        versionInfo.currentVersion,\n        versionInfo.nextVersion\n      )\n      return\n    }\n\n    debug(\n      'The current version of mkcert is %s, and the latest version is %s, mkcert will be updated',\n      versionInfo.currentVersion,\n      versionInfo.nextVersion\n    )\n\n    await this.downloadMkcert(sourceInfo.downloadUrl, this.savedMkcert)\n    versionManger.update(versionInfo.nextVersion)\n  }\n\n  private async downloadMkcert(sourceUrl: string, distPath: string) {\n    const downloader = Downloader.create()\n    await downloader.download(sourceUrl, distPath)\n  }\n\n  public async renew(hosts: string[]) {\n    const record = new Record({ config: this.config })\n\n    if (this.force) {\n      debug('Certificate is forced to regenerate')\n\n      await this.regenerate(record, hosts)\n    }\n\n    if (!record.contains(hosts)) {\n      debug(\n        `The hosts changed from [${record.getHosts()}] to [${hosts}], start regenerate certificate`\n      )\n\n      await this.regenerate(record, hosts)\n      return\n    }\n\n    const hash = await this.getLatestHash()\n\n    if (!record.equal(hash)) {\n      debug(\n        `The hash changed from ${prettyLog(record.getHash())} to ${prettyLog(\n          hash\n        )}, start regenerate certificate`\n      )\n\n      await this.regenerate(record, hosts)\n      return\n    }\n\n    debug('Neither hosts nor hash has changed, skip regenerate certificate')\n  }\n\n  /**\n   * Get certificates\n   *\n   * @param hosts host collection\n   * @returns cretificates\n   */\n  public async install(hosts: string[]) {\n    if (hosts.length) {\n      await this.renew(hosts)\n    }\n\n    return await this.getCertificate()\n  }\n}\n\nexport default Mkcert\n", "import Debug from 'debug'\n\nimport { PLUGIN_NAME } from './constant'\n\nexport const debug = Debug(PLUGIN_NAME)\n", "import path from 'node:path'\n\nimport { debug } from '../lib/logger'\nimport { readFile, writeFile, prettyLog, deepMerge } from '../lib/util'\n\nexport type RecordMate = {\n  /**\n   * The hosts that have generated certificate\n   */\n  hosts: string[]\n\n  /**\n   * file hash\n   */\n  hash?: RecordHash\n}\n\nexport type RecordHash = {\n  key?: string\n  cert?: string\n}\n\nexport type ConfigOptions = {\n  savePath: string\n}\n\nconst CONFIG_FILE_NAME = 'config.json'\n\nclass Config {\n  /**\n   * The mkcert version\n   */\n  private version: string | undefined\n\n  private record: RecordMate | undefined\n\n  private configFilePath: string\n\n  constructor({ savePath }: ConfigOptions) {\n    this.configFilePath = path.resolve(savePath, CONFIG_FILE_NAME)\n  }\n\n  public async init() {\n    const str = await readFile(this.configFilePath)\n    const options = str ? JSON.parse(str) : undefined\n\n    if (options) {\n      this.version = options.version\n      this.record = options.record\n    }\n  }\n\n  private async serialize() {\n    await writeFile(this.configFilePath, prettyLog(this))\n  }\n\n  // deep merge\n  public async merge(obj: Record<string, any>) {\n    const currentStr = prettyLog(this)\n\n    deepMerge(this, obj)\n\n    const nextStr = prettyLog(this)\n\n    debug(\n      `Receive parameter\\n ${prettyLog(\n        obj\n      )}\\nUpdate config from\\n ${currentStr} \\nto\\n ${nextStr}`\n    )\n\n    await this.serialize()\n  }\n\n  public getRecord() {\n    return this.record\n  }\n\n  public getVersion() {\n    return this.version\n  }\n}\n\nexport default Config\n", "import axios from 'axios'\n\nimport { debug } from './logger'\n\nconst request = axios.create()\n\nrequest.interceptors.response.use(\n  res => {\n    return res\n  },\n  error => {\n    debug('Request error: %o', error)\n    return Promise.reject(error)\n  }\n)\n\nexport default request\n", "import { debug } from '../lib/logger'\nimport request from '../lib/request'\nimport { writeFile } from '../lib/util'\n\nclass Downloader {\n  public static create() {\n    return new Downloader()\n  }\n\n  private constructor() {}\n\n  public async download(downloadUrl: string, savedPath: string) {\n    debug('Downloading the mkcert executable from %s', downloadUrl)\n\n    const { data } = await request.get(downloadUrl, {\n      responseType: 'arraybuffer'\n    })\n\n    await writeFile(savedPath, data)\n\n    debug('The mkcert has been saved to %s', savedPath)\n  }\n}\n\nexport default Downloader\n", "import type Config from './config'\nimport type { RecordHash, RecordMate } from './config'\n\nexport type RecordProps = {\n  config: Config\n}\nclass Record {\n  private config: Config\n\n  constructor(options: RecordProps) {\n    this.config = options.config\n  }\n\n  public getHosts() {\n    return this.config.getRecord()?.hosts\n  }\n\n  public getHash() {\n    return this.config.getRecord()?.hash\n  }\n\n  public contains(hosts: string[]) {\n    const oldHosts = this.getHosts()\n\n    if (!oldHosts) {\n      return false\n    }\n\n    // require hosts is subset of oldHosts\n    for (const host of hosts) {\n      if (!oldHosts.includes(host)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  // whether the files has been tampered with\n  public equal(hash: RecordHash) {\n    const oldHash = this.getHash()\n\n    if (!oldHash) {\n      return false\n    }\n\n    return oldHash.key === hash.key && oldHash.cert === hash.cert\n  }\n\n  public async update(record: RecordMate) {\n    await this.config.merge({ record })\n  }\n}\n\nexport default Record\n", "import request from '../lib/request'\n\nexport type SourceInfo = {\n  version: string\n  downloadUrl: string\n}\n\nexport abstract class BaseSource {\n  abstract getSourceInfo(): Promise<SourceInfo | undefined>\n\n  protected getPlatformIdentifier() {\n    const arch = process.arch === 'x64' ? 'amd64' : process.arch\n    return process.platform === 'win32'\n      ? `windows-${arch}.exe`\n      : `${process.platform}-${arch}`\n  }\n}\n\n/**\n * Download mkcert from github.com\n */\nexport class GithubSource extends BaseSource {\n  public static create() {\n    return new GithubSource()\n  }\n\n  private constructor() {\n    super()\n  }\n\n  public async getSourceInfo(): Promise<SourceInfo | undefined> {\n    const { data } = await request({\n      method: 'GET',\n      url: 'https://api.github.com/repos/FiloSottile/mkcert/releases/latest',\n    })\n    const platformIdentifier = this.getPlatformIdentifier()\n    const version = data.tag_name\n    const downloadUrl = data.assets.find((item: any) =>\n      item.name.includes(platformIdentifier)\n    )?.browser_download_url\n\n    if (!(version && downloadUrl)) {\n      return undefined\n    }\n\n    return {\n      downloadUrl,\n      version\n    }\n  }\n}\n\n/**\n * Download mkcert from coding.net\n *\n * @see https://help.coding.net/openapi\n */\nexport class CodingSource extends BaseSource {\n  public static CODING_API = 'https://e.coding.net/open-api'\n  public static CODING_AUTHORIZATION =\n    'token 000f7831ec425079439b0f55f55c729c9280d66e'\n  public static CODING_PROJECT_ID = 8524617\n  public static REPOSITORY = 'mkcert'\n\n  public static create() {\n    return new CodingSource()\n  }\n\n  private constructor() {\n    super()\n  }\n\n  private async request(data: any) {\n    return request({\n      data,\n      method: 'POST',\n      url: CodingSource.CODING_API,\n      headers: {\n        Authorization: CodingSource.CODING_AUTHORIZATION\n      }\n    })\n  }\n\n  /**\n   * Get filename of Coding.net artifacts\n   *\n   * @see https://liuweigl.coding.net/p/github/artifacts/885241/generic/packages\n   *\n   * @returns name\n   */\n  private getPackageName() {\n    return `mkcert-${this.getPlatformIdentifier()}`\n  }\n\n  async getSourceInfo(): Promise<SourceInfo | undefined> {\n    /**\n     * @see https://help.coding.net/openapi#e2106ec64e75af66f188463b1bb7e165\n     */\n    const { data: VersionData } = await this.request({\n      Action: 'DescribeArtifactVersionList',\n      ProjectId: CodingSource.CODING_PROJECT_ID,\n      Repository: CodingSource.REPOSITORY,\n      Package: this.getPackageName(),\n      PageSize: 1\n    })\n\n    const version = VersionData.Response.Data?.InstanceSet[0]?.Version\n\n    if (!version) {\n      return undefined\n    }\n\n    /**\n     * @see https://help.coding.net/openapi#63ad6bc7469373cef575e92bb92be71e\n     */\n    const { data: FileData } = await this.request({\n      Action: 'DescribeArtifactFileDownloadUrl',\n      ProjectId: CodingSource.CODING_PROJECT_ID,\n      Repository: CodingSource.REPOSITORY,\n      Package: this.getPackageName(),\n      PackageVersion: version\n    })\n\n    const downloadUrl = FileData.Response.Url\n\n    if (!downloadUrl) {\n      return undefined\n    }\n\n    return {\n      downloadUrl,\n      version\n    }\n  }\n}\n", "import { debug } from '../lib/logger'\n\nimport type Config from './config'\n\nexport type VersionMangerProps = {\n  config: Config\n}\n\nconst parseVersion = (version: string) => {\n  const str = version.trim().replace(/v/i, '')\n\n  return str.split('.')\n}\n\nclass VersionManger {\n  private config: Config\n\n  public constructor(props: VersionMangerProps) {\n    this.config = props.config\n  }\n\n  public async update(version: string) {\n    try {\n      await this.config.merge({ version })\n    } catch (err) {\n      debug('Failed to record mkcert version info: %o', err)\n    }\n  }\n\n  public compare(version: string) {\n    const currentVersion = this.config.getVersion()\n\n    if (!currentVersion) {\n      return {\n        currentVersion,\n        nextVersion: version,\n        breakingChange: false,\n        shouldUpdate: true\n      }\n    }\n\n    let breakingChange = false\n    let shouldUpdate = false\n\n    const newVersion = parseVersion(version)\n    const oldVersion = parseVersion(currentVersion)\n\n    for (let i = 0; i < newVersion.length; i++) {\n      if (newVersion[i] > oldVersion[i]) {\n        shouldUpdate = true\n        breakingChange = i === 0\n        break\n      }\n    }\n    return {\n      breakingChange,\n      shouldUpdate,\n      currentVersion,\n      nextVersion: version\n    }\n  }\n}\n\nexport default VersionManger\n"], "mappings": ";AAAA,SAAS,oBAAuC;;;ACAhD,OAAO,QAAQ;AACf,OAAO,UAAU;AAEV,IAAM,WAAW;AAEjB,IAAM,cAAc,SAAS,QAAQ,MAAM,GAAG;AAE9C,IAAM,kBAAkB,KAAK,KAAK,GAAG,QAAQ,GAAG,IAAI,QAAQ,EAAE;;;ACPrE,OAAO,mBAAyC;AAChD,OAAO,YAAY;AACnB,OAAO,QAAQ;AACf,OAAOA,SAAQ;AACf,OAAOC,WAAU;AACjB,OAAO,UAAU;AAUV,IAAM,SAAS,OAAO,aAAqB;AAChD,MAAI;AACF,UAAM,GAAG,SAAS,OAAO,QAAQ;AACjC,WAAO;AAAA,EACT,SAAS,QAAQ;AACf,WAAO;AAAA,EACT;AACF;AAEO,IAAM,QAAQ,OAAO,YAAoB;AAC9C,QAAM,UAAU,MAAM,OAAO,OAAO;AAEpC,MAAI,CAAC,SAAS;AACZ,UAAM,GAAG,SAAS,MAAM,SAAS,EAAE,WAAW,KAAK,CAAC;AAAA,EACtD;AACF;AAEO,IAAM,iBAAiB,OAAO,UAAkB,QAAQ,UAAU;AACvE,QAAM,UAAU,QAAQC,MAAK,QAAQ,QAAQ,IAAI;AACjD,QAAM,MAAM,OAAO;AACrB;AAEO,IAAM,WAAW,OAAO,aAAqB;AAClD,QAAM,UAAU,MAAM,OAAO,QAAQ;AACrC,SAAO,WAAW,MAAM,GAAG,SAAS,SAAS,QAAQ,GAAG,SAAS,IAAI;AACvE;AAEO,IAAM,YAAY,OACvB,UACA,SACG;AACH,QAAM,eAAe,UAAU,IAAI;AACnC,QAAM,GAAG,SAAS,UAAU,UAAU,IAAI;AAC1C,QAAM,GAAG,SAAS,MAAM,UAAU,GAAK;AACzC;AAEO,IAAM,UAAU,OAAO,WAAmB;AAC/C,SAAO,GAAG,SAAS,QAAQ,MAAM;AACnC;AAEO,IAAM,UAAU,OAAO,QAAgB,SAAiB;AAC7D,MAAI;AACF,UAAM,GAAG,SAAS,GAAG,QAAQ,MAAM;AAAA,MACjC,WAAW;AAAA,IACb,CAAC;AAAA,EACH,SAAS,OAAY;AAEnB,YAAQ,IAAI,GAAG,WAAW,KAAK,KAAK;AAAA,EACtC;AACF;AAEO,IAAM,OAAO,OAAO,KAAa,YAA0B;AAChE,SAAO,KAAK,UAAU,cAAc,IAAI,EAAE,KAAK,OAAO;AACxD;AAKA,IAAM,SAAS,CAAC,WAA4B;AAC1C,SAAO,WAAW,UAAU,WAAW;AACzC;AAEO,IAAM,gBAAgB,MAAM;AACjC,QAAM,gBAAgBC,IAAG,kBAAkB;AAC3C,QAAM,YAAsB,CAAC;AAC7B,aAAW,OAAO,eAAe;AAC/B,UAAM,aAAa,cAAc,GAAG;AACpC,QAAI,YAAY;AACd,iBAAW,QAAQ,YAAY;AAC7B,YAAI,OAAO,KAAK,MAAM,GAAG;AACvB,oBAAU,KAAK,KAAK,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,IAAM,kBAAkB,MAAM;AACnC,SAAO,CAAC,aAAa,GAAG,cAAc,CAAC;AACzC;AAEO,IAAM,UAAU,OAAO,aAAqB;AACjD,QAAM,UAAU,MAAM,SAAS,QAAQ;AAEvC,MAAI,SAAS;AACX,UAAM,OAAO,OAAO,WAAW,QAAQ;AACvC,SAAK,OAAO,OAAO;AACnB,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAM,QAAQ,CAAC,QACb,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAE1C,IAAM,WAAW,CAAC,QAAa,WAAgB;AAC7C,MAAI,EAAE,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AACrC,WAAO;AAAA,EACT;AAEA,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,YAAM,cAAc,OAAO,GAAG;AAC9B,YAAM,cAAc,OAAO,GAAG;AAE9B,UAAI,MAAM,WAAW,KAAK,MAAM,WAAW,GAAG;AAC5C,iBAAS,aAAa,WAAW;AAAA,MACnC,OAAO;AACL,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACF;AAEO,IAAM,YAAY,CAAC,WAAgB,WAAkB;AAC1D,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM,SAAS,GAAG,CAAC,GAAG,MAAM;AACvD;AAEO,IAAM,YAAY,CAAC,QAA8B;AACtD,SAAO,KAAK,UAAU,KAAK,MAAM,CAAC;AACpC;AAEO,IAAM,YAAY,CAACD,UAAkB;AAC1C,SAAO,IAAIA,KAAI;AACjB;;;AC9IA,OAAOE,WAAU;AACjB,OAAOC,cAAa;AAEpB,OAAO,QAAQ;;;ACHf,OAAO,WAAW;AAIX,IAAM,QAAQ,MAAM,WAAW;;;ACJtC,OAAOC,WAAU;AA0BjB,IAAM,mBAAmB;AAEzB,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIH;AAAA,EAEA;AAAA,EAEA;AAAA,EAER,YAAY,EAAE,SAAS,GAAkB;AACvC,SAAK,iBAAiBC,MAAK,QAAQ,UAAU,gBAAgB;AAAA,EAC/D;AAAA,EAEA,MAAa,OAAO;AAClB,UAAM,MAAM,MAAM,SAAS,KAAK,cAAc;AAC9C,UAAM,UAAU,MAAM,KAAK,MAAM,GAAG,IAAI;AAExC,QAAI,SAAS;AACX,WAAK,UAAU,QAAQ;AACvB,WAAK,SAAS,QAAQ;AAAA,IACxB;AAAA,EACF;AAAA,EAEA,MAAc,YAAY;AACxB,UAAM,UAAU,KAAK,gBAAgB,UAAU,IAAI,CAAC;AAAA,EACtD;AAAA;AAAA,EAGA,MAAa,MAAM,KAA0B;AAC3C,UAAM,aAAa,UAAU,IAAI;AAEjC,cAAU,MAAM,GAAG;AAEnB,UAAM,UAAU,UAAU,IAAI;AAE9B;AAAA,MACE;AAAA,GAAuB;AAAA,QACrB;AAAA,MACF,CAAC;AAAA;AAAA,GAA0B,UAAU;AAAA;AAAA,GAAW,OAAO;AAAA,IACzD;AAEA,UAAM,KAAK,UAAU;AAAA,EACvB;AAAA,EAEO,YAAY;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,aAAa;AAClB,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,iBAAQ;;;AClFf,OAAO,WAAW;AAIlB,IAAM,UAAU,MAAM,OAAO;AAE7B,QAAQ,aAAa,SAAS;AAAA,EAC5B,SAAO;AACL,WAAO;AAAA,EACT;AAAA,EACA,WAAS;AACP,UAAM,qBAAqB,KAAK;AAChC,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC7B;AACF;AAEA,IAAO,kBAAQ;;;ACZf,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAc,SAAS;AACrB,WAAO,IAAI,YAAW;AAAA,EACxB;AAAA,EAEQ,cAAc;AAAA,EAAC;AAAA,EAEvB,MAAa,SAAS,aAAqB,WAAmB;AAC5D,UAAM,6CAA6C,WAAW;AAE9D,UAAM,EAAE,KAAK,IAAI,MAAM,gBAAQ,IAAI,aAAa;AAAA,MAC9C,cAAc;AAAA,IAChB,CAAC;AAED,UAAM,UAAU,WAAW,IAAI;AAE/B,UAAM,mCAAmC,SAAS;AAAA,EACpD;AACF;AAEA,IAAO,qBAAQ;;;AClBf,IAAM,SAAN,MAAa;AAAA,EACH;AAAA,EAER,YAAY,SAAsB;AAChC,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EAEO,WAAW;AAChB,WAAO,KAAK,OAAO,UAAU,GAAG;AAAA,EAClC;AAAA,EAEO,UAAU;AACf,WAAO,KAAK,OAAO,UAAU,GAAG;AAAA,EAClC;AAAA,EAEO,SAAS,OAAiB;AAC/B,UAAM,WAAW,KAAK,SAAS;AAE/B,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAGA,eAAW,QAAQ,OAAO;AACxB,UAAI,CAAC,SAAS,SAAS,IAAI,GAAG;AAC5B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGO,MAAM,MAAkB;AAC7B,UAAM,UAAU,KAAK,QAAQ;AAE7B,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,QAAQ,KAAK,OAAO,QAAQ,SAAS,KAAK;AAAA,EAC3D;AAAA,EAEA,MAAa,OAAO,QAAoB;AACtC,UAAM,KAAK,OAAO,MAAM,EAAE,OAAO,CAAC;AAAA,EACpC;AACF;AAEA,IAAO,iBAAQ;;;AC/CR,IAAe,aAAf,MAA0B;AAAA,EAGrB,wBAAwB;AAChC,UAAM,OAAO,QAAQ,SAAS,QAAQ,UAAU,QAAQ;AACxD,WAAO,QAAQ,aAAa,UACxB,WAAW,IAAI,SACf,GAAG,QAAQ,QAAQ,IAAI,IAAI;AAAA,EACjC;AACF;AAKO,IAAM,eAAN,MAAM,sBAAqB,WAAW;AAAA,EAC3C,OAAc,SAAS;AACrB,WAAO,IAAI,cAAa;AAAA,EAC1B;AAAA,EAEQ,cAAc;AACpB,UAAM;AAAA,EACR;AAAA,EAEA,MAAa,gBAAiD;AAC5D,UAAM,EAAE,KAAK,IAAI,MAAM,gBAAQ;AAAA,MAC7B,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,CAAC;AACD,UAAM,qBAAqB,KAAK,sBAAsB;AACtD,UAAM,UAAU,KAAK;AACrB,UAAM,cAAc,KAAK,OAAO;AAAA,MAAK,CAAC,SACpC,KAAK,KAAK,SAAS,kBAAkB;AAAA,IACvC,GAAG;AAEH,QAAI,EAAE,WAAW,cAAc;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAOO,IAAM,eAAN,MAAM,sBAAqB,WAAW;AAAA,EAC3C,OAAc,aAAa;AAAA,EAC3B,OAAc,uBACZ;AAAA,EACF,OAAc,oBAAoB;AAAA,EAClC,OAAc,aAAa;AAAA,EAE3B,OAAc,SAAS;AACrB,WAAO,IAAI,cAAa;AAAA,EAC1B;AAAA,EAEQ,cAAc;AACpB,UAAM;AAAA,EACR;AAAA,EAEA,MAAc,QAAQ,MAAW;AAC/B,WAAO,gBAAQ;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR,KAAK,cAAa;AAAA,MAClB,SAAS;AAAA,QACP,eAAe,cAAa;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,iBAAiB;AACvB,WAAO,UAAU,KAAK,sBAAsB,CAAC;AAAA,EAC/C;AAAA,EAEA,MAAM,gBAAiD;AAIrD,UAAM,EAAE,MAAM,YAAY,IAAI,MAAM,KAAK,QAAQ;AAAA,MAC/C,QAAQ;AAAA,MACR,WAAW,cAAa;AAAA,MACxB,YAAY,cAAa;AAAA,MACzB,SAAS,KAAK,eAAe;AAAA,MAC7B,UAAU;AAAA,IACZ,CAAC;AAED,UAAM,UAAU,YAAY,SAAS,MAAM,YAAY,CAAC,GAAG;AAE3D,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAKA,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ;AAAA,MAC5C,QAAQ;AAAA,MACR,WAAW,cAAa;AAAA,MACxB,YAAY,cAAa;AAAA,MACzB,SAAS,KAAK,eAAe;AAAA,MAC7B,gBAAgB;AAAA,IAClB,CAAC;AAED,UAAM,cAAc,SAAS,SAAS;AAEtC,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;AC9HA,IAAM,eAAe,CAAC,YAAoB;AACxC,QAAM,MAAM,QAAQ,KAAK,EAAE,QAAQ,MAAM,EAAE;AAE3C,SAAO,IAAI,MAAM,GAAG;AACtB;AAEA,IAAM,gBAAN,MAAoB;AAAA,EACV;AAAA,EAED,YAAY,OAA2B;AAC5C,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA,EAEA,MAAa,OAAO,SAAiB;AACnC,QAAI;AACF,YAAM,KAAK,OAAO,MAAM,EAAE,QAAQ,CAAC;AAAA,IACrC,SAAS,KAAK;AACZ,YAAM,4CAA4C,GAAG;AAAA,IACvD;AAAA,EACF;AAAA,EAEO,QAAQ,SAAiB;AAC9B,UAAM,iBAAiB,KAAK,OAAO,WAAW;AAE9C,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,QACL;AAAA,QACA,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAEA,QAAI,iBAAiB;AACrB,QAAI,eAAe;AAEnB,UAAM,aAAa,aAAa,OAAO;AACvC,UAAM,aAAa,aAAa,cAAc;AAE9C,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,WAAW,CAAC,IAAI,WAAW,CAAC,GAAG;AACjC,uBAAe;AACf,yBAAiB,MAAM;AACvB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AACF;AAEA,IAAO,kBAAQ;;;APYf,IAAM,SAAN,MAAM,QAAO;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EAER,OAAc,OAAO,SAAwB;AAC3C,WAAO,IAAI,QAAO,OAAO;AAAA,EAC3B;AAAA,EAEQ,YAAY,SAAwB;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX,cAAc;AAAA,MACd,eAAe;AAAA,MACf;AAAA,IACF,IAAI;AAEJ,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,WAAWC,MAAK,QAAQ,QAAQ;AACrC,SAAK,cAAcA,MAAK,QAAQ,UAAU,WAAW;AACrD,SAAK,eAAeA,MAAK,QAAQ,UAAU,YAAY;AACvD,SAAK,aAAa,UAAU;AAE5B,QAAI,KAAK,eAAe,UAAU;AAChC,WAAK,SAAS,aAAa,OAAO;AAAA,IACpC,WAAW,KAAK,eAAe,UAAU;AACvC,WAAK,SAAS,aAAa,OAAO;AAAA,IACpC,OAAO;AACL,WAAK,SAAS,KAAK;AAAA,IACrB;AAEA,SAAK,cAAcA,MAAK;AAAA,MACtB;AAAA,MACAC,SAAQ,aAAa,UAAU,eAAe;AAAA,IAChD;AAEA,SAAK,SAAS,IAAI,eAAO,EAAE,UAAU,KAAK,SAAS,CAAC;AAAA,EACtD;AAAA,EAEA,MAAc,kBAAkB;AAC9B,QAAI;AAEJ,QAAI,KAAK,aAAa;AACpB,UAAI,MAAM,OAAO,KAAK,WAAW,GAAG;AAClC,iBAAS,KAAK;AAAA,MAChB,OAAO;AACL,aAAK,OAAO;AAAA,UACV,GAAG;AAAA,YACD,GAAG,KAAK,WAAW;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,MAAM,OAAO,KAAK,WAAW,GAAG;AACzC,eAAS,KAAK;AAAA,IAChB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAc,gBAAgB;AAC5B,UAAM,QAAQ,MAAM,QAAQ,KAAK,QAAQ;AACzC,WAAO,MAAM,KAAK,UAAQ,KAAK,SAAS,QAAQ,CAAC;AAAA,EACnD;AAAA,EAEA,MAAc,kBAAkB;AAC9B,QAAI,MAAM,KAAK,cAAc,GAAG;AAC9B;AAAA,IACF;AAEA,UAAM,eAAe,MAAM,KAAK,gBAAgB;AAChD,UAAM,mBAAmB,GAAG,UAAU,YAAY,CAAC;AAEnD,UAAM,QAAQ,gBAAgB,EAAE;AAEhC,UAAM,gBAAgB,MAAM,KAAK,gBAAgB;AACjD,UAAM,YAAYD,MAAK;AAAA,MACrB,cAAc,OAAO,SAAS,EAAE,QAAQ,OAAO,EAAE;AAAA,IACnD;AAEA,QAAI,cAAc,KAAK,UAAU;AAC/B;AAAA,IACF;AAEA,UAAM,cAAc,MAAM,OAAO,SAAS;AAE1C,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AAEA,UAAM,QAAQ,WAAW,KAAK,QAAQ;AAAA,EACxC;AAAA,EAEA,MAAc,iBAAiB;AAC7B,UAAM,MAAM,MAAM,SAAS,KAAK,WAAW;AAC3C,UAAM,OAAO,MAAM,SAAS,KAAK,YAAY;AAE7C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAc,kBAAkB,OAAiB;AAC/C,UAAM,QAAQ,MAAM,KAAK,GAAG;AAC5B,UAAM,eAAe,MAAM,KAAK,gBAAgB;AAEhD,QAAI,CAAC,cAAc;AACjB;AAAA,QACE,6DAA6D,KAAK;AAAA,MACpE;AAAA,IACF;AAEA,UAAM,eAAe,KAAK,QAAQ;AAClC,UAAM,KAAK,gBAAgB;AAE3B,UAAM,MAAM,GAAG,UAAU,YAAY,CAAC,uBAAuB;AAAA,MAC3D,KAAK;AAAA,IACP,CAAC,eAAe,UAAU,KAAK,YAAY,CAAC,IAAI,KAAK;AAErD,UAAM,KAAK,KAAK;AAAA,MACd,KAAK;AAAA,QACH,GAAGC,SAAQ;AAAA,QACX,QAAQ,KAAK;AAAA,QACb,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAED,SAAK,OAAO;AAAA,MACV;AAAA,EAAiC,KAAK,WAAW;AAAA,EAAK,KAAK,YAAY;AAAA,IACzE;AAAA,EACF;AAAA,EAEQ,gBAAgB,YAAY;AAClC,WAAO;AAAA,MACL,KAAK,MAAM,QAAQ,KAAK,WAAW;AAAA,MACnC,MAAM,MAAM,QAAQ,KAAK,YAAY;AAAA,IACvC;AAAA,EACF;AAAA,EAEA,MAAc,WAAW,QAAgB,OAAiB;AACxD,UAAM,KAAK,kBAAkB,KAAK;AAElC,UAAM,OAAO,MAAM,KAAK,cAAc;AAEtC,WAAO,OAAO,EAAE,OAAO,KAAK,CAAC;AAAA,EAC/B;AAAA,EAEA,MAAa,OAAO;AAClB,UAAM,eAAe,KAAK,QAAQ;AAClC,UAAM,KAAK,OAAO,KAAK;AAEvB,UAAM,eAAe,MAAM,KAAK,gBAAgB;AAEhD,QAAI,CAAC,cAAc;AACjB,YAAM,KAAK,WAAW;AAAA,IACxB,WAAW,KAAK,aAAa;AAC3B,YAAM,KAAK,cAAc;AAAA,IAC3B;AAAA,EACF;AAAA,EAEA,MAAc,gBAAgB;AAC5B,UAAM,aAAa,MAAM,KAAK,OAAO,cAAc;AAEnD,QAAI,CAAC,YAAY;AACf,YAAM,UACJ,OAAO,KAAK,eAAe,WACvB,0DAA0DA,SAAQ,QACpE,kBAAkBA,SAAQ,IAAI,YAAY,KAAK,eAAe,WAC1D,mDACA,sFACJ,KACE;AACN,YAAM,IAAI,MAAM,OAAO;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAc,aAAa;AACzB,UAAM,aAAa,MAAM,KAAK,cAAc;AAE5C,UAAM,4CAA4C;AAElD,UAAM,KAAK,eAAe,WAAW,aAAa,KAAK,WAAW;AAAA,EACpE;AAAA,EAEA,MAAc,gBAAgB;AAC5B,UAAM,gBAAgB,IAAI,gBAAc,EAAE,QAAQ,KAAK,OAAO,CAAC;AAC/D,UAAM,aAAa,MAAM,KAAK,cAAc;AAE5C,QAAI,CAAC,YAAY;AACf,WAAK,OAAO;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF;AAEA,UAAM,cAAc,cAAc,QAAQ,WAAW,OAAO;AAE5D,QAAI,CAAC,YAAY,cAAc;AAC7B,YAAM,+CAA+C;AACrD;AAAA,IACF;AAEA,QAAI,YAAY,gBAAgB;AAC9B;AAAA,QACE;AAAA,QACA,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA;AAAA,IACF;AAEA;AAAA,MACE;AAAA,MACA,YAAY;AAAA,MACZ,YAAY;AAAA,IACd;AAEA,UAAM,KAAK,eAAe,WAAW,aAAa,KAAK,WAAW;AAClE,kBAAc,OAAO,YAAY,WAAW;AAAA,EAC9C;AAAA,EAEA,MAAc,eAAe,WAAmB,UAAkB;AAChE,UAAM,aAAa,mBAAW,OAAO;AACrC,UAAM,WAAW,SAAS,WAAW,QAAQ;AAAA,EAC/C;AAAA,EAEA,MAAa,MAAM,OAAiB;AAClC,UAAM,SAAS,IAAI,eAAO,EAAE,QAAQ,KAAK,OAAO,CAAC;AAEjD,QAAI,KAAK,OAAO;AACd,YAAM,qCAAqC;AAE3C,YAAM,KAAK,WAAW,QAAQ,KAAK;AAAA,IACrC;AAEA,QAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AAC3B;AAAA,QACE,2BAA2B,OAAO,SAAS,CAAC,SAAS,KAAK;AAAA,MAC5D;AAEA,YAAM,KAAK,WAAW,QAAQ,KAAK;AACnC;AAAA,IACF;AAEA,UAAM,OAAO,MAAM,KAAK,cAAc;AAEtC,QAAI,CAAC,OAAO,MAAM,IAAI,GAAG;AACvB;AAAA,QACE,yBAAyB,UAAU,OAAO,QAAQ,CAAC,CAAC,OAAO;AAAA,UACzD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,KAAK,WAAW,QAAQ,KAAK;AACnC;AAAA,IACF;AAEA,UAAM,iEAAiE;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAa,QAAQ,OAAiB;AACpC,QAAI,MAAM,QAAQ;AAChB,YAAM,KAAK,MAAM,KAAK;AAAA,IACxB;AAEA,WAAO,MAAM,KAAK,eAAe;AAAA,EACnC;AACF;AAEA,IAAO,iBAAQ;;;AHlWf,IAAM,SAAS,CAAC,UAA+B,CAAC,MAAoB;AAClE,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ,OAAO,EAAE,SAAS,CAAC,GAAG,SAAS,MAAM;AAE3C,UAAI,OAAO,OAAO,UAAU,aAAa,OAAO,UAAU,OAAO;AAC/D;AAAA,MACF;AAEA,YAAM,EAAE,QAAQ,CAAC,GAAG,GAAG,cAAc,IAAI;AAEzC,YAAM,SAAS,aAAa,UAAU;AAAA,QACpC,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,SAAS,eAAO,OAAO;AAAA,QAC3B;AAAA,QACA,GAAG;AAAA,MACL,CAAC;AAED,YAAM,OAAO,KAAK;AAElB,YAAM,WAAW,CAAC,GAAG,gBAAgB,GAAG,GAAG,KAAK;AAEhD,UAAI,OAAO,OAAO,SAAS,UAAU;AACnC,iBAAS,KAAK,OAAO,IAAI;AAAA,MAC3B;AAEA,YAAM,cAAc,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,OAAO,OAAO;AAEhE,YAAM,cAAc,MAAM,OAAO,QAAQ,WAAW;AACpD,YAAM,cAAc;AAAA,QAClB,KAAK,YAAY,OAAO,OAAO,KAAK,YAAY,GAAG;AAAA,QACnD,MAAM,YAAY,QAAQ,OAAO,KAAK,YAAY,IAAI;AAAA,MACxD;AAEA,aAAO;AAAA,QACL,QAAQ;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,gBAAQ;", "names": ["os", "path", "path", "os", "path", "process", "path", "path", "path", "process"]}