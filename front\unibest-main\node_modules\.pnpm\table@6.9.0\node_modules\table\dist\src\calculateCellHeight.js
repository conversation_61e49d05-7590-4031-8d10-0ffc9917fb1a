"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateCellHeight = void 0;
const wrapCell_1 = require("./wrapCell");
/**
 * Calculates height of cell content in regard to its width and word wrapping.
 */
const calculateCellHeight = (value, columnWidth, useWrapWord = false) => {
    return (0, wrapCell_1.wrapCell)(value, columnWidth, useWrapWord).length;
};
exports.calculateCellHeight = calculateCellHeight;
//# sourceMappingURL=calculateCellHeight.js.map