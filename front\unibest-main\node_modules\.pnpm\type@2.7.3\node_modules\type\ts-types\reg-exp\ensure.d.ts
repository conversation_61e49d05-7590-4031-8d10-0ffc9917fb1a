import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureRegExp(value: any, options?: EnsureBaseOptions): RegExp;
declare function ensureRegExp(value: any, options?: EnsureBaseOptions & EnsureIsOptional): RegExp | null;
declare function ensureRegExp(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<RegExp>): RegExp;

export default ensureRegExp;
