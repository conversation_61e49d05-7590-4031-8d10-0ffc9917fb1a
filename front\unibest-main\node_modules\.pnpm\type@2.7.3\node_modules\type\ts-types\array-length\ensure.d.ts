import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureArrayLength(value: any, options?: EnsureBaseOptions): number;
declare function ensureArrayLength(value: any, options?: EnsureBaseOptions & EnsureIsOptional): number | null;
declare function ensureArrayLength(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<number>): number;

export default ensureArrayLength;
