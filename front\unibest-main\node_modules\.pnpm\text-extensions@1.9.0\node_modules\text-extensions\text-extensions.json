["applescript", "asp", "aspx", "atom", "bashrc", "bat", "bbcolors", "bib", "bower<PERSON>", "c", "cbl", "cc", "cfc", "cfg", "cfm", "cmd", "cnf", "cob", "coffee", "conf", "cpp", "cson", "css", "csslintrc", "csv", "curlrc", "cxx", "diff", "eco", "editorconfig", "ejs", "emacs", "eml", "erb", "erl", "<PERSON><PERSON><PERSON><PERSON>", "eslintrc", "gemrc", "gitattributes", "gitconfig", "gitignore", "go", "gvimrc", "h", "haml", "hbs", "hgignore", "hpp", "htaccess", "htm", "html", "iced", "ini", "ino", "irbrc", "itermcolors", "jade", "js", "jscsrc", "jshintignore", "jshintrc", "json", "j<PERSON>ld", "jsx", "less", "log", "ls", "m", "markdown", "md", "mdown", "mdwn", "mdx", "mht", "mhtml", "mjs", "mkd", "mkdn", "mkdown", "nfo", "npmignore", "npmrc", "nvmrc", "patch", "pbxproj", "pch", "php", "phtml", "pl", "pm", "properties", "py", "rb", "rdoc", "rdoc_options", "rlib", "ron", "rs", "rss", "rst", "rtf", "rvmrc", "sass", "scala", "scss", "seestyle", "sh", "sls", "sql", "sss", "strings", "styl", "stylus", "sub", "sublime-build", "sublime-commands", "sublime-completions", "sublime-keymap", "sublime-macro", "sublime-menu", "sublime-project", "sublime-settings", "sublime-workspace", "svg", "terminal", "tex", "text", "textile", "tmLanguage", "tmTheme", "ts", "tsv", "tsx", "txt", "vbs", "vim", "viminfo", "vimrc", "vue", "webapp", "xht", "xhtml", "xml", "xsl", "yaml", "yml", "zsh", "zshrc"]