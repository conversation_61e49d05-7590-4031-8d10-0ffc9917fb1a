import type { Logger } from 'vite';
import { type BaseSource } from './source';
export type SourceType = 'github' | 'coding' | BaseSource;
export type MkcertBaseOptions = {
    /**
     * Whether to force generate
     */
    force?: boolean;
    /**
     * Automatically upgrade mkcert
     *
     * @default false
     */
    autoUpgrade?: boolean;
    /**
     * Specify mkcert download source
     *
     * @default github
     */
    source?: SourceType;
    /**
     * If your network is restricted, you can specify a local binary file instead of downloading, it should be an absolute path
     *
     * @default none
     */
    mkcertPath?: string;
    /**
     * The location to save the files, such as key and cert files
     */
    savePath?: string;
    /**
     * The name of private key file generated by mkcert
     */
    keyFileName?: string;
    /**
     * The name of cert file generated by mkcert
     */
    certFileName?: string;
};
export type MkcertOptions = MkcertBaseOptions & {
    logger: Logger;
};
declare class Mkcert {
    private force?;
    private autoUpgrade?;
    private sourceType;
    private savePath;
    private logger;
    private source;
    private localMkcert?;
    private savedMkcert;
    private keyFilePath;
    private certFilePath;
    private config;
    static create(options: MkcertOptions): Mkcert;
    private constructor();
    private getMkcertBinary;
    private checkCAExists;
    private retainExistedCA;
    private getCertificate;
    private createCertificate;
    private getLatestHash;
    private regenerate;
    init(): Promise<void>;
    private getSourceInfo;
    private initMkcert;
    private upgradeMkcert;
    private downloadMkcert;
    renew(hosts: string[]): Promise<void>;
    /**
     * Get certificates
     *
     * @param hosts host collection
     * @returns cretificates
     */
    install(hosts: string[]): Promise<{
        key: string;
        cert: string;
    }>;
}
export default Mkcert;
