export * from '@unocss-applet/preset-applet';
export * from '@unocss-applet/preset-rem-rpx';
export * from '@unocss-applet/transformer-attributify';
export { default as transformerApplet } from '@unocss-applet/transformer-applet';
import { UserConfig } from 'unocss';

interface AppletConfig {
    /**
     * Platform
     * @default 'uniapp'
     */
    platform?: 'uniapp' | 'taro';
    /**
     * Unsupported characters in applet, will be added to the default value
     * @default ['.', ':', '%', '!', '#', '(', ')', '[', '/', ']', ',', '$', '{', '}', '@', '+', '^', '&', '<', '>', '\'', '\\']
     */
    unsupportedChars?: string[];
    /**
     * Space Between and Divide Width Elements
     * @default ['view', 'button', 'text', 'image']
     */
    betweenElements?: string[];
}
type UserAppletConfig<Theme extends object = object> = UserConfig<Theme> & {
    applet?: AppletConfig;
};

export type { AppletConfig, UserAppletConfig };
