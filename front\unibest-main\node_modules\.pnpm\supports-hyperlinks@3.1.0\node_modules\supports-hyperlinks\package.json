{"name": "supports-hyperlinks", "version": "3.1.0", "description": "Detect if your terminal emulator supports hyperlinks", "license": "MIT", "repository": "jamestalmage/supports-hyperlinks", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=14.18"}, "scripts": {"prepublishOnly": "npm run create-types", "test": "xo && nyc ava && tsc", "create-types": "tsc --project declaration.tsconfig.json"}, "files": ["index.js", "index.d.ts", "browser.js"], "browser": "browser.js", "keywords": ["link", "terminal", "hyperlink", "cli"], "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "devDependencies": {"@tsconfig/node14": "^1.0.3", "@types/supports-color": "^8.1.1", "ava": "^2.2.0", "codecov": "^3.5.0", "nyc": "^14.1.1", "typescript": "^4.9.5", "xo": "^0.24.0"}, "nyc": {"reporter": ["lcov", "text"]}}