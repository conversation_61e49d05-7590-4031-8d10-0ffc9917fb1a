{"name": "unocss", "version": "0.58.9", "description": "The instant on-demand Atomic CSS engine.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss"}, "sponsor": {"url": "https://github.com/sponsors/antfu"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "atomic-css", "atomic-css-engine", "css", "tailwind", "windicss"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./preset-attributify": {"types": "./dist/preset-attributify.d.ts", "import": "./dist/preset-attributify.mjs", "require": "./dist/preset-attributify.cjs"}, "./preset-tagify": {"types": "./dist/preset-tagify.d.ts", "import": "./dist/preset-tagify.mjs", "require": "./dist/preset-tagify.cjs"}, "./preset-icons": {"types": "./dist/preset-icons.d.ts", "import": "./dist/preset-icons.mjs", "require": "./dist/preset-icons.cjs"}, "./preset-mini": {"types": "./dist/preset-mini.d.ts", "import": "./dist/preset-mini.mjs", "require": "./dist/preset-mini.cjs"}, "./preset-typography": {"types": "./dist/preset-typography.d.ts", "import": "./dist/preset-typography.mjs", "require": "./dist/preset-typography.cjs"}, "./preset-uno": {"types": "./dist/preset-uno.d.ts", "import": "./dist/preset-uno.mjs", "require": "./dist/preset-uno.cjs"}, "./preset-web-fonts": {"types": "./dist/preset-web-fonts.d.ts", "import": "./dist/preset-web-fonts.mjs", "require": "./dist/preset-web-fonts.cjs"}, "./preset-wind": {"types": "./dist/preset-wind.d.ts", "import": "./dist/preset-wind.mjs", "require": "./dist/preset-wind.cjs"}, "./vite": {"types": "./dist/vite.d.ts", "import": "./dist/vite.mjs", "require": "./dist/vite.cjs"}, "./astro": {"types": "./dist/astro.d.ts", "import": "./dist/astro.mjs", "require": "./dist/astro.cjs"}, "./webpack": {"types": "./dist/webpack.d.ts", "import": "./dist/webpack.mjs", "require": "./dist/webpack.cjs"}, "./postcss": {"types": "./dist/postcss.d.ts", "import": "./dist/postcss.mjs", "require": "./dist/postcss.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "index.d.ts", "files": ["*.d.ts", "dist"], "engines": {"node": ">=14"}, "peerDependencies": {"vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0", "@unocss/webpack": "0.58.9"}, "peerDependenciesMeta": {"@unocss/webpack": {"optional": true}, "vite": {"optional": true}}, "dependencies": {"@unocss/cli": "0.58.9", "@unocss/astro": "0.58.9", "@unocss/core": "0.58.9", "@unocss/extractor-arbitrary-variants": "0.58.9", "@unocss/postcss": "0.58.9", "@unocss/preset-attributify": "0.58.9", "@unocss/preset-mini": "0.58.9", "@unocss/preset-icons": "0.58.9", "@unocss/preset-tagify": "0.58.9", "@unocss/preset-uno": "0.58.9", "@unocss/preset-typography": "0.58.9", "@unocss/preset-web-fonts": "0.58.9", "@unocss/preset-wind": "0.58.9", "@unocss/reset": "0.58.9", "@unocss/transformer-attributify-jsx-babel": "0.58.9", "@unocss/transformer-variant-group": "0.58.9", "@unocss/transformer-attributify-jsx": "0.58.9", "@unocss/transformer-directives": "0.58.9", "@unocss/vite": "0.58.9", "@unocss/transformer-compile-class": "0.58.9"}, "devDependencies": {"vite": "^5.2.7", "@unocss/webpack": "0.58.9"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}