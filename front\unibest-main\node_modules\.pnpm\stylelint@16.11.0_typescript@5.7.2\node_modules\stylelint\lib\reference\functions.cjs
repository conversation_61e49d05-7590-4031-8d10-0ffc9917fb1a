// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const camelCaseFunctions = new Set([
	'translateX',
	'translateY',
	'translateZ',
	'scaleX',
	'scaleY',
	'scaleZ',
	'rotateX',
	'rotateY',
	'rotateZ',
	'skewX',
	'skewY',
]);

const colorFunctions = new Set([
	'color',
	'color-mix',
	'hsl',
	'hsla',
	'hwb',
	'lab',
	'lch',
	'oklab',
	'oklch',
	'rgb',
	'rgba',
]);

const singleArgumentMathFunctions = new Set([
	'abs',
	'acos',
	'asin',
	'atan',
	'calc',
	'cos',
	'exp',
	'sign',
	'sin',
	'sqrt',
	'tan',
]);

const mathFunctions = new Set([
	...singleArgumentMathFunctions,
	'atan2',
	'calc-size',
	'clamp',
	'hypot',
	'log',
	'max',
	'min',
	'mod',
	'pow',
	'rem',
	'round',
]);

exports.camelCaseFunctions = camelCaseFunctions;
exports.colorFunctions = colorFunctions;
exports.mathFunctions = mathFunctions;
