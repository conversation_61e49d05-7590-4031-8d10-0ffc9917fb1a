{"name": "tiny-pinyin", "version": "1.3.2", "description": "convert hanzi to pinyin", "main": "dist/index.js", "typings": "tiny-pinyin.d.ts", "files": ["dist", "src", "tiny-pinyin.d.ts"], "directories": {"doc": "docs", "test": "test"}, "devDependencies": {"autoprefixer": "^7.2.6", "babel-cli": "^6.26.0", "babel-loader": "^7.1.5", "babel-preset-es2015": "^6.24.1", "babel-preset-power-assert": "^1.0.0", "css-loader": "^0.28.11", "eslint": "^4.19.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^4.2.3", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "extract-text-webpack-plugin": "^2.1.2", "full-icu": "^1.3.1", "html-webpack-plugin": "^2.30.1", "karma": "^1.7.1", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.3.0", "karma-mocha": "^1.3.0", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-webpack": "^2.0.13", "mocha": "^3.5.3", "postcss-loader": "^2.1.6", "power-assert": "^1.6.1", "style-loader": "^0.17.0", "webpack": "^2.7.0"}, "scripts": {"pretest": "babel --presets=power-assert,es2015 test --out-dir test-compiled", "test": "npm run lint && npm run test-notsupport && npm run test-common", "test-common": "NODE_ICU_DATA='node_modules/full-icu' mocha test-compiled/index.spec.js", "test-notsupport": "mocha test-compiled/notSupport.spec.js", "test:browser": "karma start scripts/karma.conf.js --single-run", "test:sauce": "SAUCE='saucelabs' karma start scripts/karma.conf.js", "lint": "eslint src test scripts/{*.js,pre-commit}", "prepublish": "babel --presets=es2015 src --out-dir dist", "build:doc": "webpack --config scripts/webpack.config.js --display-error-details", "install-hook": "cd scripts && chmod +x install-hooks.sh && ./install-hooks.sh", "uninstall-hook": "cd scripts && chmod +x uninstall-hooks.sh && ./uninstall-hooks.sh"}, "repository": {"type": "git", "url": "git+https://github.com/creeperyang/pinyin.git"}, "keywords": ["pinyin", "hanzi", "拼音", "汉语", "汉字", "中文"], "author": "creeperyang", "license": "MIT", "bugs": {"url": "https://github.com/creeperyang/pinyin/issues"}, "homepage": "https://github.com/creeperyang/pinyin#readme"}