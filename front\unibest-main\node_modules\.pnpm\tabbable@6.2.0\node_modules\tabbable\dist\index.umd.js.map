{"version": 3, "file": "index.umd.js", "sources": ["../src/index.js"], "sourcesContent": ["// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n"], "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "getSortOrderTabIndex", "isScope", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusable", "isTabbable", "focusableCandidateSelector", "isFocusable"], "mappings": ";;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA,IAAMA,kBAAkB,GAAG,CACzB,oBAAoB,EACpB,qBAAqB,EACrB,uBAAuB,EACvB,sBAAsB,EACtB,qBAAqB,EACrB,mCAAmC,EACnC,8BAA8B,EAC9B,8BAA8B,EAC9B,+DAA+D,EAC/D,4CAA4C,EAC5C,sBAAsB,CACvB,CAAA;EACD,IAAMC,iBAAiB,kBAAmBD,kBAAkB,CAACE,IAAI,CAAC,GAAG,CAAC,CAAA;EAEtE,IAAMC,SAAS,GAAG,OAAOC,OAAO,KAAK,WAAW,CAAA;EAEhD,IAAMC,OAAO,GAAGF,SAAS,GACrB,YAAY,EAAE,GACdC,OAAO,CAACE,SAAS,CAACD,OAAO,IACzBD,OAAO,CAACE,SAAS,CAACC,iBAAiB,IACnCH,OAAO,CAACE,SAAS,CAACE,qBAAqB,CAAA;EAE3C,IAAMC,WAAW,GACf,CAACN,SAAS,IAAIC,OAAO,CAACE,SAAS,CAACG,WAAW,GACvC,UAACC,OAAO,EAAA;EAAA,EAAA,IAAAC,oBAAA,CAAA;EAAA,EAAA,OAAKD,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAAC,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,oBAAA,GAAPD,OAAO,CAAED,WAAW,MAAA,IAAA,IAAAE,oBAAA,KAApBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAAC,IAAA,CAAAF,OAAuB,CAAC,CAAA;EAAA,CAAA,GACrC,UAACA,OAAO,EAAA;EAAA,EAAA,OAAKA,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAPA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEG,aAAa,CAAA;EAAA,CAAA,CAAA;;EAEzC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAaC,IAAI,EAAEC,MAAM,EAAS;EAAA,EAAA,IAAAC,kBAAA,CAAA;EAAA,EAAA,IAAfD,MAAM,KAAA,KAAA,CAAA,EAAA;EAANA,IAAAA,MAAM,GAAG,IAAI,CAAA;EAAA,GAAA;EAC3C;EACA;EACA;IACA,IAAME,QAAQ,GAAGH,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAAE,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,kBAAA,GAAJF,IAAI,CAAEI,YAAY,MAAAF,IAAAA,IAAAA,kBAAA,uBAAlBA,kBAAA,CAAAL,IAAA,CAAAG,IAAI,EAAiB,OAAO,CAAC,CAAA;IAC9C,IAAMK,KAAK,GAAGF,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,MAAM,CAAA;;EAEpD;EACA;EACA;EACA;EACA;EACA,EAAA,IAAMG,MAAM,GAAGD,KAAK,IAAKJ,MAAM,IAAID,IAAI,IAAID,OAAO,CAACC,IAAI,CAACO,UAAU,CAAE,CAAC;;EAErE,EAAA,OAAOD,MAAM,CAAA;EACf,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA,IAAME,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaR,IAAI,EAAE;EAAA,EAAA,IAAAS,mBAAA,CAAA;EACxC;EACA;EACA;IACA,IAAMC,QAAQ,GAAGV,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAAS,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,mBAAA,GAAJT,IAAI,CAAEI,YAAY,MAAAK,IAAAA,IAAAA,mBAAA,uBAAlBA,mBAAA,CAAAZ,IAAA,CAAAG,IAAI,EAAiB,iBAAiB,CAAC,CAAA;EACxD,EAAA,OAAOU,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,MAAM,CAAA;EAC/C,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,EAAE,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;EAC5D;EACA;EACA,EAAA,IAAIf,OAAO,CAACa,EAAE,CAAC,EAAE;EACf,IAAA,OAAO,EAAE,CAAA;EACX,GAAA;EAEA,EAAA,IAAIG,UAAU,GAAGC,KAAK,CAACzB,SAAS,CAAC0B,KAAK,CAACC,KAAK,CAC1CN,EAAE,CAACO,gBAAgB,CAACjC,iBAAiB,CACvC,CAAC,CAAA;IACD,IAAI2B,gBAAgB,IAAIvB,OAAO,CAACO,IAAI,CAACe,EAAE,EAAE1B,iBAAiB,CAAC,EAAE;EAC3D6B,IAAAA,UAAU,CAACK,OAAO,CAACR,EAAE,CAAC,CAAA;EACxB,GAAA;EACAG,EAAAA,UAAU,GAAGA,UAAU,CAACD,MAAM,CAACA,MAAM,CAAC,CAAA;EACtC,EAAA,OAAOC,UAAU,CAAA;EACnB,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,IAAMM,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAC5BC,QAAQ,EACRT,gBAAgB,EAChBU,OAAO,EACP;IACA,IAAMR,UAAU,GAAG,EAAE,CAAA;EACrB,EAAA,IAAMS,eAAe,GAAGR,KAAK,CAACS,IAAI,CAACH,QAAQ,CAAC,CAAA;IAC5C,OAAOE,eAAe,CAACE,MAAM,EAAE;EAC7B,IAAA,IAAM/B,OAAO,GAAG6B,eAAe,CAACG,KAAK,EAAE,CAAA;EACvC,IAAA,IAAI5B,OAAO,CAACJ,OAAO,EAAE,KAAK,CAAC,EAAE;EAC3B;EACA;EACA,MAAA,SAAA;EACF,KAAA;EAEA,IAAA,IAAIA,OAAO,CAACiC,OAAO,KAAK,MAAM,EAAE;EAC9B;EACA,MAAA,IAAMC,QAAQ,GAAGlC,OAAO,CAACmC,gBAAgB,EAAE,CAAA;QAC3C,IAAMC,OAAO,GAAGF,QAAQ,CAACH,MAAM,GAAGG,QAAQ,GAAGlC,OAAO,CAACqC,QAAQ,CAAA;QAC7D,IAAMC,gBAAgB,GAAGZ,wBAAwB,CAACU,OAAO,EAAE,IAAI,EAAER,OAAO,CAAC,CAAA;QACzE,IAAIA,OAAO,CAACW,OAAO,EAAE;UACnBnB,UAAU,CAACoB,IAAI,CAAAjB,KAAA,CAAfH,UAAU,EAASkB,gBAAgB,CAAC,CAAA;EACtC,OAAC,MAAM;UACLlB,UAAU,CAACoB,IAAI,CAAC;EACdC,UAAAA,WAAW,EAAEzC,OAAO;EACpBoB,UAAAA,UAAU,EAAEkB,gBAAAA;EACd,SAAC,CAAC,CAAA;EACJ,OAAA;EACF,KAAC,MAAM;EACL;QACA,IAAMI,cAAc,GAAG/C,OAAO,CAACO,IAAI,CAACF,OAAO,EAAET,iBAAiB,CAAC,CAAA;EAC/D,MAAA,IACEmD,cAAc,IACdd,OAAO,CAACT,MAAM,CAACnB,OAAO,CAAC,KACtBkB,gBAAgB,IAAI,CAACS,QAAQ,CAACgB,QAAQ,CAAC3C,OAAO,CAAC,CAAC,EACjD;EACAoB,QAAAA,UAAU,CAACoB,IAAI,CAACxC,OAAO,CAAC,CAAA;EAC1B,OAAA;;EAEA;EACA,MAAA,IAAM4C,UAAU,GACd5C,OAAO,CAAC4C,UAAU;EAClB;QACC,OAAOhB,OAAO,CAACiB,aAAa,KAAK,UAAU,IAC1CjB,OAAO,CAACiB,aAAa,CAAC7C,OAAO,CAAE,CAAA;;EAEnC;EACA;EACA;QACA,IAAM8C,eAAe,GACnB,CAAC1C,OAAO,CAACwC,UAAU,EAAE,KAAK,CAAC,KAC1B,CAAChB,OAAO,CAACmB,gBAAgB,IAAInB,OAAO,CAACmB,gBAAgB,CAAC/C,OAAO,CAAC,CAAC,CAAA;QAElE,IAAI4C,UAAU,IAAIE,eAAe,EAAE;EACjC;EACA;EACA;EACA;EACA;EACA;EACA,QAAA,IAAMR,iBAAgB,GAAGZ,wBAAwB,CAC/CkB,UAAU,KAAK,IAAI,GAAG5C,OAAO,CAACqC,QAAQ,GAAGO,UAAU,CAACP,QAAQ,EAC5D,IAAI,EACJT,OACF,CAAC,CAAA;UAED,IAAIA,OAAO,CAACW,OAAO,EAAE;YACnBnB,UAAU,CAACoB,IAAI,CAAAjB,KAAA,CAAfH,UAAU,EAASkB,iBAAgB,CAAC,CAAA;EACtC,SAAC,MAAM;YACLlB,UAAU,CAACoB,IAAI,CAAC;EACdC,YAAAA,WAAW,EAAEzC,OAAO;EACpBoB,YAAAA,UAAU,EAAEkB,iBAAAA;EACd,WAAC,CAAC,CAAA;EACJ,SAAA;EACF,OAAC,MAAM;EACL;EACA;UACAT,eAAe,CAACJ,OAAO,CAAAF,KAAA,CAAvBM,eAAe,EAAY7B,OAAO,CAACqC,QAAQ,CAAC,CAAA;EAC9C,OAAA;EACF,KAAA;EACF,GAAA;EACA,EAAA,OAAOjB,UAAU,CAAA;EACnB,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA,IAAM4B,WAAW,GAAG,SAAdA,WAAWA,CAAa3C,IAAI,EAAE;EAClC,EAAA,OAAO,CAAC4C,KAAK,CAACC,QAAQ,CAAC7C,IAAI,CAACI,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;EAC5D,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;AACA,MAAM0C,WAAW,GAAG,SAAdA,WAAWA,CAAa9C,IAAI,EAAE;IAClC,IAAI,CAACA,IAAI,EAAE;EACT,IAAA,MAAM,IAAI+C,KAAK,CAAC,kBAAkB,CAAC,CAAA;EACrC,GAAA;EAEA,EAAA,IAAI/C,IAAI,CAACgD,QAAQ,GAAG,CAAC,EAAE;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;MACA,IACE,CAAC,yBAAyB,CAACC,IAAI,CAACjD,IAAI,CAAC4B,OAAO,CAAC,IAC3CpB,iBAAiB,CAACR,IAAI,CAAC,KACzB,CAAC2C,WAAW,CAAC3C,IAAI,CAAC,EAClB;EACA,MAAA,OAAO,CAAC,CAAA;EACV,KAAA;EACF,GAAA;IAEA,OAAOA,IAAI,CAACgD,QAAQ,CAAA;EACtB,EAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAME,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAalD,IAAI,EAAEmD,OAAO,EAAE;EACpD,EAAA,IAAMH,QAAQ,GAAGF,WAAW,CAAC9C,IAAI,CAAC,CAAA;IAElC,IAAIgD,QAAQ,GAAG,CAAC,IAAIG,OAAO,IAAI,CAACR,WAAW,CAAC3C,IAAI,CAAC,EAAE;EACjD,IAAA,OAAO,CAAC,CAAA;EACV,GAAA;EAEA,EAAA,OAAOgD,QAAQ,CAAA;EACjB,CAAC,CAAA;EAED,IAAMI,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAaC,CAAC,EAAEC,CAAC,EAAE;IAC3C,OAAOD,CAAC,CAACL,QAAQ,KAAKM,CAAC,CAACN,QAAQ,GAC5BK,CAAC,CAACE,aAAa,GAAGD,CAAC,CAACC,aAAa,GACjCF,CAAC,CAACL,QAAQ,GAAGM,CAAC,CAACN,QAAQ,CAAA;EAC7B,CAAC,CAAA;EAED,IAAMQ,OAAO,GAAG,SAAVA,OAAOA,CAAaxD,IAAI,EAAE;EAC9B,EAAA,OAAOA,IAAI,CAAC4B,OAAO,KAAK,OAAO,CAAA;EACjC,CAAC,CAAA;EAED,IAAM6B,aAAa,GAAG,SAAhBA,aAAaA,CAAazD,IAAI,EAAE;IACpC,OAAOwD,OAAO,CAACxD,IAAI,CAAC,IAAIA,IAAI,CAAC0D,IAAI,KAAK,QAAQ,CAAA;EAChD,CAAC,CAAA;EAED,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAa3D,IAAI,EAAE;IAC3C,IAAM4D,CAAC,GACL5D,IAAI,CAAC4B,OAAO,KAAK,SAAS,IAC1BZ,KAAK,CAACzB,SAAS,CAAC0B,KAAK,CAClBC,KAAK,CAAClB,IAAI,CAACgC,QAAQ,CAAC,CACpB6B,IAAI,CAAC,UAACC,KAAK,EAAA;EAAA,IAAA,OAAKA,KAAK,CAAClC,OAAO,KAAK,SAAS,CAAA;KAAC,CAAA,CAAA;EACjD,EAAA,OAAOgC,CAAC,CAAA;EACV,CAAC,CAAA;EAED,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,KAAK,EAAEC,IAAI,EAAE;EAC7C,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACtC,MAAM,EAAEwC,CAAC,EAAE,EAAE;EACrC,IAAA,IAAIF,KAAK,CAACE,CAAC,CAAC,CAACC,OAAO,IAAIH,KAAK,CAACE,CAAC,CAAC,CAACD,IAAI,KAAKA,IAAI,EAAE;QAC9C,OAAOD,KAAK,CAACE,CAAC,CAAC,CAAA;EACjB,KAAA;EACF,GAAA;EACF,CAAC,CAAA;EAED,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAapE,IAAI,EAAE;EACtC,EAAA,IAAI,CAACA,IAAI,CAACqE,IAAI,EAAE;EACd,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IACA,IAAMC,UAAU,GAAGtE,IAAI,CAACiE,IAAI,IAAIvE,WAAW,CAACM,IAAI,CAAC,CAAA;EACjD,EAAA,IAAMuE,WAAW,GAAG,SAAdA,WAAWA,CAAaF,IAAI,EAAE;MAClC,OAAOC,UAAU,CAACnD,gBAAgB,CAChC,4BAA4B,GAAGkD,IAAI,GAAG,IACxC,CAAC,CAAA;KACF,CAAA;EAED,EAAA,IAAIG,QAAQ,CAAA;IACZ,IACE,OAAOC,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACC,GAAG,KAAK,WAAW,IACjC,OAAOD,MAAM,CAACC,GAAG,CAACC,MAAM,KAAK,UAAU,EACvC;EACAH,IAAAA,QAAQ,GAAGD,WAAW,CAACE,MAAM,CAACC,GAAG,CAACC,MAAM,CAAC3E,IAAI,CAACqE,IAAI,CAAC,CAAC,CAAA;EACtD,GAAC,MAAM;MACL,IAAI;EACFG,MAAAA,QAAQ,GAAGD,WAAW,CAACvE,IAAI,CAACqE,IAAI,CAAC,CAAA;OAClC,CAAC,OAAOO,GAAG,EAAE;EACZ;QACAC,OAAO,CAACC,KAAK,CACX,0IAA0I,EAC1IF,GAAG,CAACG,OACN,CAAC,CAAA;EACD,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EACF,GAAA;IAEA,IAAMZ,OAAO,GAAGJ,eAAe,CAACS,QAAQ,EAAExE,IAAI,CAACiE,IAAI,CAAC,CAAA;EACpD,EAAA,OAAO,CAACE,OAAO,IAAIA,OAAO,KAAKnE,IAAI,CAAA;EACrC,CAAC,CAAA;EAED,IAAMgF,OAAO,GAAG,SAAVA,OAAOA,CAAahF,IAAI,EAAE;IAC9B,OAAOwD,OAAO,CAACxD,IAAI,CAAC,IAAIA,IAAI,CAAC0D,IAAI,KAAK,OAAO,CAAA;EAC/C,CAAC,CAAA;EAED,IAAMuB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAajF,IAAI,EAAE;IACzC,OAAOgF,OAAO,CAAChF,IAAI,CAAC,IAAI,CAACoE,eAAe,CAACpE,IAAI,CAAC,CAAA;EAChD,CAAC,CAAA;;EAED;EACA,IAAMkF,cAAc,GAAG,SAAjBA,cAAcA,CAAalF,IAAI,EAAE;EAAA,EAAA,IAAAmF,SAAA,CAAA;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,IAAIC,QAAQ,GAAGpF,IAAI,IAAIN,WAAW,CAACM,IAAI,CAAC,CAAA;IACxC,IAAIqF,YAAY,GAAAF,CAAAA,SAAA,GAAGC,QAAQ,cAAAD,SAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAARA,SAAA,CAAUG,IAAI,CAAA;;EAEjC;EACA;IACA,IAAIC,QAAQ,GAAG,KAAK,CAAA;EACpB,EAAA,IAAIH,QAAQ,IAAIA,QAAQ,KAAKpF,IAAI,EAAE;EAAA,IAAA,IAAAwF,aAAA,EAAAC,qBAAA,EAAAC,mBAAA,CAAA;EACjCH,IAAAA,QAAQ,GAAG,CAAC,EACV,CAAAC,aAAA,GAAAH,YAAY,MAAAG,IAAAA,IAAAA,aAAA,gBAAAC,qBAAA,GAAZD,aAAA,CAAc1F,aAAa,cAAA2F,qBAAA,KAAA,KAAA,CAAA,IAA3BA,qBAAA,CAA6BE,QAAQ,CAACN,YAAY,CAAC,IACnDrF,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAA0F,KAAAA,CAAAA,IAAAA,CAAAA,mBAAA,GAAJ1F,IAAI,CAAEF,aAAa,MAAA4F,IAAAA,IAAAA,mBAAA,eAAnBA,mBAAA,CAAqBC,QAAQ,CAAC3F,IAAI,CAAC,CACpC,CAAA;EAED,IAAA,OAAO,CAACuF,QAAQ,IAAIF,YAAY,EAAE;EAAA,MAAA,IAAAO,UAAA,EAAAC,cAAA,EAAAC,qBAAA,CAAA;EAChC;EACA;EACA;EACAV,MAAAA,QAAQ,GAAG1F,WAAW,CAAC2F,YAAY,CAAC,CAAA;QACpCA,YAAY,GAAA,CAAAO,UAAA,GAAGR,QAAQ,cAAAQ,UAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAARA,UAAA,CAAUN,IAAI,CAAA;QAC7BC,QAAQ,GAAG,CAAC,EAAAM,CAAAA,cAAA,GAACR,YAAY,MAAA,IAAA,IAAAQ,cAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,qBAAA,GAAZD,cAAA,CAAc/F,aAAa,cAAAgG,qBAAA,KAAA,KAAA,CAAA,IAA3BA,qBAAA,CAA6BH,QAAQ,CAACN,YAAY,CAAC,CAAA,CAAA;EAClE,KAAA;EACF,GAAA;EAEA,EAAA,OAAOE,QAAQ,CAAA;EACjB,CAAC,CAAA;EAED,IAAMQ,UAAU,GAAG,SAAbA,UAAUA,CAAa/F,IAAI,EAAE;EACjC,EAAA,IAAAgG,qBAAA,GAA0BhG,IAAI,CAACiG,qBAAqB,EAAE;MAA9CC,KAAK,GAAAF,qBAAA,CAALE,KAAK;MAAEC,MAAM,GAAAH,qBAAA,CAANG,MAAM,CAAA;EACrB,EAAA,OAAOD,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,CAAA;EACpC,CAAC,CAAA;EACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAapG,IAAI,EAAAqG,IAAA,EAAmC;EAAA,EAAA,IAA/BC,YAAY,GAAAD,IAAA,CAAZC,YAAY;MAAE9D,aAAa,GAAA6D,IAAA,CAAb7D,aAAa,CAAA;EAC5D;EACA;EACA;EACA;EACA;IACA,IAAI+D,gBAAgB,CAACvG,IAAI,CAAC,CAACwG,UAAU,KAAK,QAAQ,EAAE;EAClD,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,IAAMC,eAAe,GAAGnH,OAAO,CAACO,IAAI,CAACG,IAAI,EAAE,+BAA+B,CAAC,CAAA;IAC3E,IAAM0G,gBAAgB,GAAGD,eAAe,GAAGzG,IAAI,CAAC2G,aAAa,GAAG3G,IAAI,CAAA;IACpE,IAAIV,OAAO,CAACO,IAAI,CAAC6G,gBAAgB,EAAE,uBAAuB,CAAC,EAAE;EAC3D,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,IACE,CAACJ,YAAY,IACbA,YAAY,KAAK,MAAM,IACvBA,YAAY,KAAK,aAAa,EAC9B;EACA,IAAA,IAAI,OAAO9D,aAAa,KAAK,UAAU,EAAE;EACvC;EACA;QACA,IAAMoE,YAAY,GAAG5G,IAAI,CAAA;EACzB,MAAA,OAAOA,IAAI,EAAE;EACX,QAAA,IAAM2G,aAAa,GAAG3G,IAAI,CAAC2G,aAAa,CAAA;EACxC,QAAA,IAAME,QAAQ,GAAGnH,WAAW,CAACM,IAAI,CAAC,CAAA;EAClC,QAAA,IACE2G,aAAa,IACb,CAACA,aAAa,CAACpE,UAAU,IACzBC,aAAa,CAACmE,aAAa,CAAC,KAAK,IAAI;YACrC;EACA;EACA;YACA,OAAOZ,UAAU,CAAC/F,IAAI,CAAC,CAAA;EACzB,SAAC,MAAM,IAAIA,IAAI,CAAC8G,YAAY,EAAE;EAC5B;YACA9G,IAAI,GAAGA,IAAI,CAAC8G,YAAY,CAAA;WACzB,MAAM,IAAI,CAACH,aAAa,IAAIE,QAAQ,KAAK7G,IAAI,CAACF,aAAa,EAAE;EAC5D;YACAE,IAAI,GAAG6G,QAAQ,CAACvB,IAAI,CAAA;EACtB,SAAC,MAAM;EACL;EACAtF,UAAAA,IAAI,GAAG2G,aAAa,CAAA;EACtB,SAAA;EACF,OAAA;EAEA3G,MAAAA,IAAI,GAAG4G,YAAY,CAAA;EACrB,KAAA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA,IAAA,IAAI1B,cAAc,CAAClF,IAAI,CAAC,EAAE;EACxB;EACA;EACA;EACA;EACA,MAAA,OAAO,CAACA,IAAI,CAAC+G,cAAc,EAAE,CAACrF,MAAM,CAAA;EACtC,KAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACA,IAAI4E,YAAY,KAAK,aAAa,EAAE;QAClC,OAAO,IAAI,CAAC;EACd,KAAA;EACA;EACF,GAAC,MAAM,IAAIA,YAAY,KAAK,eAAe,EAAE;EAC3C;EACA;EACA;EACA;EACA;MACA,OAAOP,UAAU,CAAC/F,IAAI,CAAC,CAAA;EACzB,GAAA;;EAEA;EACA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAC,CAAA;;EAED;EACA;EACA;EACA,IAAMgH,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAahH,IAAI,EAAE;IAC7C,IAAI,kCAAkC,CAACiD,IAAI,CAACjD,IAAI,CAAC4B,OAAO,CAAC,EAAE;EACzD,IAAA,IAAIrB,UAAU,GAAGP,IAAI,CAAC2G,aAAa,CAAA;EACnC;EACA,IAAA,OAAOpG,UAAU,EAAE;QACjB,IAAIA,UAAU,CAACqB,OAAO,KAAK,UAAU,IAAIrB,UAAU,CAAC0G,QAAQ,EAAE;EAC5D;EACA,QAAA,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,UAAU,CAACyB,QAAQ,CAACN,MAAM,EAAEwC,CAAC,EAAE,EAAE;YACnD,IAAMJ,KAAK,GAAGvD,UAAU,CAACyB,QAAQ,CAACkF,IAAI,CAAChD,CAAC,CAAC,CAAA;EACzC;EACA,UAAA,IAAIJ,KAAK,CAAClC,OAAO,KAAK,QAAQ,EAAE;EAC9B;EACA;EACA,YAAA,OAAOtC,OAAO,CAACO,IAAI,CAACU,UAAU,EAAE,sBAAsB,CAAC,GACnD,IAAI,GACJ,CAACuD,KAAK,CAAC6B,QAAQ,CAAC3F,IAAI,CAAC,CAAA;EAC3B,WAAA;EACF,SAAA;EACA;EACA,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;QACAO,UAAU,GAAGA,UAAU,CAACoG,aAAa,CAAA;EACvC,KAAA;EACF,GAAA;;EAEA;EACA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAC,CAAA;EAED,IAAMQ,+BAA+B,GAAG,SAAlCA,+BAA+BA,CAAa5F,OAAO,EAAEvB,IAAI,EAAE;IAC/D,IACEA,IAAI,CAACiH,QAAQ;EACb;EACA;EACA;EACAlH,EAAAA,OAAO,CAACC,IAAI,CAAC,IACbyD,aAAa,CAACzD,IAAI,CAAC,IACnBoG,QAAQ,CAACpG,IAAI,EAAEuB,OAAO,CAAC;EACvB;IACAoC,oBAAoB,CAAC3D,IAAI,CAAC,IAC1BgH,sBAAsB,CAAChH,IAAI,CAAC,EAC5B;EACA,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;EAED,IAAMoH,8BAA8B,GAAG,SAAjCA,8BAA8BA,CAAa7F,OAAO,EAAEvB,IAAI,EAAE;EAC9D,EAAA,IACEiF,kBAAkB,CAACjF,IAAI,CAAC,IACxB8C,WAAW,CAAC9C,IAAI,CAAC,GAAG,CAAC,IACrB,CAACmH,+BAA+B,CAAC5F,OAAO,EAAEvB,IAAI,CAAC,EAC/C;EACA,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;EAED,IAAMqH,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAaC,cAAc,EAAE;EAC1D,EAAA,IAAMtE,QAAQ,GAAGH,QAAQ,CAACyE,cAAc,CAAClH,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAA;IACtE,IAAIwC,KAAK,CAACI,QAAQ,CAAC,IAAIA,QAAQ,IAAI,CAAC,EAAE;EACpC,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EACA;EACA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA,IAAMuE,WAAW,GAAG,SAAdA,WAAWA,CAAaxG,UAAU,EAAE;IACxC,IAAMyG,gBAAgB,GAAG,EAAE,CAAA;IAC3B,IAAMC,gBAAgB,GAAG,EAAE,CAAA;EAC3B1G,EAAAA,UAAU,CAAC2G,OAAO,CAAC,UAAUR,IAAI,EAAEhD,CAAC,EAAE;EACpC,IAAA,IAAMf,OAAO,GAAG,CAAC,CAAC+D,IAAI,CAAC9E,WAAW,CAAA;MAClC,IAAMzC,OAAO,GAAGwD,OAAO,GAAG+D,IAAI,CAAC9E,WAAW,GAAG8E,IAAI,CAAA;EACjD,IAAA,IAAMS,iBAAiB,GAAGzE,oBAAoB,CAACvD,OAAO,EAAEwD,OAAO,CAAC,CAAA;MAChE,IAAM7B,QAAQ,GAAG6B,OAAO,GAAGoE,WAAW,CAACL,IAAI,CAACnG,UAAU,CAAC,GAAGpB,OAAO,CAAA;MACjE,IAAIgI,iBAAiB,KAAK,CAAC,EAAE;EAC3BxE,MAAAA,OAAO,GACHqE,gBAAgB,CAACrF,IAAI,CAAAjB,KAAA,CAArBsG,gBAAgB,EAASlG,QAAQ,CAAC,GAClCkG,gBAAgB,CAACrF,IAAI,CAACxC,OAAO,CAAC,CAAA;EACpC,KAAC,MAAM;QACL8H,gBAAgB,CAACtF,IAAI,CAAC;EACpBoB,QAAAA,aAAa,EAAEW,CAAC;EAChBlB,QAAAA,QAAQ,EAAE2E,iBAAiB;EAC3BT,QAAAA,IAAI,EAAEA,IAAI;EACV/D,QAAAA,OAAO,EAAEA,OAAO;EAChBpB,QAAAA,OAAO,EAAET,QAAAA;EACX,OAAC,CAAC,CAAA;EACJ,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,OAAOmG,gBAAgB,CACpBG,IAAI,CAACxE,oBAAoB,CAAC,CAC1ByE,MAAM,CAAC,UAACC,GAAG,EAAEC,QAAQ,EAAK;MACzBA,QAAQ,CAAC5E,OAAO,GACZ2E,GAAG,CAAC3F,IAAI,CAAAjB,KAAA,CAAR4G,GAAG,EAASC,QAAQ,CAAChG,OAAO,CAAC,GAC7B+F,GAAG,CAAC3F,IAAI,CAAC4F,QAAQ,CAAChG,OAAO,CAAC,CAAA;EAC9B,IAAA,OAAO+F,GAAG,CAAA;EACZ,GAAC,EAAE,EAAE,CAAC,CACLE,MAAM,CAACR,gBAAgB,CAAC,CAAA;EAC7B,CAAC,CAAA;AAEKS,MAAAA,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAE3G,OAAO,EAAE;EAC7CA,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EAEvB,EAAA,IAAIR,UAAU,CAAA;IACd,IAAIQ,OAAO,CAACiB,aAAa,EAAE;MACzBzB,UAAU,GAAGM,wBAAwB,CACnC,CAAC6G,SAAS,CAAC,EACX3G,OAAO,CAACV,gBAAgB,EACxB;QACEC,MAAM,EAAEsG,8BAA8B,CAACe,IAAI,CAAC,IAAI,EAAE5G,OAAO,CAAC;EAC1DW,MAAAA,OAAO,EAAE,KAAK;QACdM,aAAa,EAAEjB,OAAO,CAACiB,aAAa;EACpCE,MAAAA,gBAAgB,EAAE2E,yBAAAA;EACpB,KACF,CAAC,CAAA;EACH,GAAC,MAAM;EACLtG,IAAAA,UAAU,GAAGJ,aAAa,CACxBuH,SAAS,EACT3G,OAAO,CAACV,gBAAgB,EACxBuG,8BAA8B,CAACe,IAAI,CAAC,IAAI,EAAE5G,OAAO,CACnD,CAAC,CAAA;EACH,GAAA;IACA,OAAOgG,WAAW,CAACxG,UAAU,CAAC,CAAA;EAChC,EAAC;AAEKqH,MAAAA,SAAS,GAAG,SAAZA,SAASA,CAAaF,SAAS,EAAE3G,OAAO,EAAE;EAC9CA,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EAEvB,EAAA,IAAIR,UAAU,CAAA;IACd,IAAIQ,OAAO,CAACiB,aAAa,EAAE;MACzBzB,UAAU,GAAGM,wBAAwB,CACnC,CAAC6G,SAAS,CAAC,EACX3G,OAAO,CAACV,gBAAgB,EACxB;QACEC,MAAM,EAAEqG,+BAA+B,CAACgB,IAAI,CAAC,IAAI,EAAE5G,OAAO,CAAC;EAC3DW,MAAAA,OAAO,EAAE,IAAI;QACbM,aAAa,EAAEjB,OAAO,CAACiB,aAAAA;EACzB,KACF,CAAC,CAAA;EACH,GAAC,MAAM;EACLzB,IAAAA,UAAU,GAAGJ,aAAa,CACxBuH,SAAS,EACT3G,OAAO,CAACV,gBAAgB,EACxBsG,+BAA+B,CAACgB,IAAI,CAAC,IAAI,EAAE5G,OAAO,CACpD,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,OAAOR,UAAU,CAAA;EACnB,EAAC;AAEKsH,MAAAA,UAAU,GAAG,SAAbA,UAAUA,CAAarI,IAAI,EAAEuB,OAAO,EAAE;EAC1CA,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;IACvB,IAAI,CAACvB,IAAI,EAAE;EACT,IAAA,MAAM,IAAI+C,KAAK,CAAC,kBAAkB,CAAC,CAAA;EACrC,GAAA;IACA,IAAIzD,OAAO,CAACO,IAAI,CAACG,IAAI,EAAEd,iBAAiB,CAAC,KAAK,KAAK,EAAE;EACnD,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACA,EAAA,OAAOkI,8BAA8B,CAAC7F,OAAO,EAAEvB,IAAI,CAAC,CAAA;EACtD,EAAC;EAED,IAAMsI,0BAA0B,kBAAmBrJ,kBAAkB,CAClE+I,MAAM,CAAC,QAAQ,CAAC,CAChB7I,IAAI,CAAC,GAAG,CAAC,CAAA;AAENoJ,MAAAA,WAAW,GAAG,SAAdA,WAAWA,CAAavI,IAAI,EAAEuB,OAAO,EAAE;EAC3CA,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;IACvB,IAAI,CAACvB,IAAI,EAAE;EACT,IAAA,MAAM,IAAI+C,KAAK,CAAC,kBAAkB,CAAC,CAAA;EACrC,GAAA;IACA,IAAIzD,OAAO,CAACO,IAAI,CAACG,IAAI,EAAEsI,0BAA0B,CAAC,KAAK,KAAK,EAAE;EAC5D,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACA,EAAA,OAAOnB,+BAA+B,CAAC5F,OAAO,EAAEvB,IAAI,CAAC,CAAA;EACvD;;;;;;;;;;;;;;"}