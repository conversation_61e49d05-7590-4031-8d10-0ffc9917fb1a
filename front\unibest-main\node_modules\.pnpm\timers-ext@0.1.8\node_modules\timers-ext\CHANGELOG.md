# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.1.8](https://github.com/medikoo/timers-ext/compare/v0.1.7...v0.1.8) (2024-06-03)

### Maintenance Improvements

- Bump dependencies ([1644a83](https://github.com/medikoo/timers-ext/commit/1644a83a63dbcb239140d0574014438cf58dddca))
- Declare minimum Node.js version ([2cb991d](https://github.com/medikoo/timers-ext/commit/2cb991d1b7a835a4a332d1d77932ad39dc54e487))

<a name="0.1.7"></a>

## [0.1.7](https://github.com/medikoo/timers-ext/compare/v0.1.6...v0.1.7) (2018-10-04)

### Bug Fixes

- typo in error message ([36720cf](https://github.com/medikoo/timers-ext/commit/36720cf))

<a name="0.1.6"></a>

## [0.1.6](https://github.com/medikoo/timers-ext/compare/v0.1.5...v0.1.6) (2018-10-04)

### Features

- report timeout value on error ([abb4afa](https://github.com/medikoo/timers-ext/commit/abb4afa))

<a name="0.1.5"></a>

## [0.1.5](https://github.com/medikoo/timers-ext/compare/v0.1.4...v0.1.5) (2018-03-13)

### Features

- **promise:** sleep util ([c50d575](https://github.com/medikoo/timers-ext/commit/c50d575))

<a name="0.1.4"></a>

## [0.1.4](https://github.com/medikoo/timers-ext/compare/v0.1.3...v0.1.4) (2018-03-08)

### Bug Fixes

- **promise:** clear timeout on promise resolution ([6301a6b](https://github.com/medikoo/timers-ext/commit/6301a6b))

<a name="0.1.3"></a>

## [0.1.3](https://github.com/medikoo/timers-ext/compare/v0.1.2...v0.1.3) (2018-03-07)

### Features

- **promise:** promise.timeout method ([3f52d27](https://github.com/medikoo/timers-ext/commit/3f52d27))

## Changelog for previous versions

See `CHANGES` file
