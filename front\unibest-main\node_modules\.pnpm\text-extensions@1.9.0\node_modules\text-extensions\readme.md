# text-extensions [![Build Status](https://travis-ci.org/sindresorhus/text-extensions.svg?branch=master)](https://travis-ci.org/sindresorhus/text-extensions)

> List of text file extensions

The list is just a [JSON file](text-extensions.json) and can be used wherever.


## Install

```
$ npm install text-extensions
```


## Usage

```js
const textExtensions = require('text-extensions');

console.log(textExtensions);
//=> ['asp', 'aspx', ...]
```


## Related

- [is-text-path](https://github.com/sindresorhus/is-text-path) - Check if a filepath is a text file
- [binary-extensions](https://github.com/sindresorhus/binary-extensions) - List of binary file extensions


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
